# ChatBot Full Stack Setup Guide

This guide will help you set up and run the complete ChatBot application with both frontend and backend services.

## 🏗️ Architecture Overview

- **Frontend**: React + TypeScript + Vite + TailwindCSS (Port 5173)
- **Backend**: Go + Echo + PostgreSQL + Redis (Port 8080)
- **Database**: PostgreSQL (Port 5432)
- **Cache**: Redis (Port 6379)
- **Admin**: pgAdmin (Port 5050)

## 📋 Prerequisites

- **Node.js 18+** and npm
- **Docker** and Docker Compose
- **Git**

## 🚀 Quick Start

### 1. Start Backend Services

```bash
# Make the script executable (if not already)
chmod +x start-backend.sh

# Start all backend services
./start-backend.sh
```

This will start:
- PostgreSQL database
- Redis cache
- Go Web API server
- pgAdmin (database management)

### 2. Start Frontend Development Server

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies (if not already done)
npm install

# Start development server
npm run dev
```

The frontend will be available at: **http://localhost:5173**

## 🔧 Manual Setup (Alternative)

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd backend/web-api
   ```

2. **Start services with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Verify services are running**
   ```bash
   docker-compose ps
   curl http://localhost:8080/health
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Create environment file**
   ```bash
   cp .env.example .env
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

## 🧪 Testing the Application

### 1. Test Backend API

```bash
# Health check
curl http://localhost:8080/health

# Register a new user
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "password123",
    "first_name": "Test",
    "last_name": "User"
  }'

# Login
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 2. Test Frontend

1. Open **http://localhost:5173** in your browser
2. Click "Create a new account"
3. Fill in the registration form
4. After registration, you'll be redirected to the chat interface
5. Try sending a message in the chat

## 🔗 API Integration

The frontend is configured to connect to the backend API at `http://localhost:8080/api/v1`.

### Key Endpoints Used:

- **Authentication**:
  - `POST /auth/register` - User registration
  - `POST /auth/login` - User login
  - `POST /auth/logout` - User logout
  - `POST /auth/refresh` - Refresh token

- **Chat**:
  - `POST /chat/messages` - Send message
  - `GET /chat/messages` - Get messages
  - `PUT /chat/messages/:id` - Update message
  - `DELETE /chat/messages/:id` - Delete message

## 🛠️ Development

### Frontend Development

```bash
cd frontend

# Start development server with hot reload
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run linting
npm run lint
```

### Backend Development

```bash
cd backend/web-api

# View logs
docker-compose logs -f web-api

# Restart specific service
docker-compose restart web-api

# Stop all services
docker-compose down

# Rebuild and restart
docker-compose up -d --build
```

## 🗄️ Database Management

### Using pgAdmin

1. Open **http://localhost:5050**
2. Login with:
   - Email: `<EMAIL>`
   - Password: `admin123`
3. Add server connection:
   - Host: `postgres`
   - Port: `5432`
   - Database: `web_api`
   - Username: `postgres`
   - Password: `postgres123`

### Direct PostgreSQL Access

```bash
# Connect to PostgreSQL container
docker exec -it web-api-postgres psql -U postgres -d web_api

# View tables
\dt

# View users
SELECT * FROM users;

# View messages
SELECT * FROM messages;
```

## 🔧 Configuration

### Frontend Environment Variables

```env
# frontend/.env
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_APP_NAME=ChatBot
VITE_APP_VERSION=1.0.0
```

### Backend Environment Variables

Backend configuration is handled in `backend/web-api/docker-compose.yml`. Key settings:

- `JWT_SECRET_KEY`: Change in production!
- `DB_PASSWORD`: Database password
- `SERVER_PORT`: API server port

## 🚨 Troubleshooting

### Common Issues

1. **Port conflicts**: Make sure ports 5173, 8080, 5432, 6379, 5050 are available
2. **Docker not running**: Start Docker Desktop
3. **Database connection issues**: Wait for PostgreSQL to fully start (check logs)
4. **CORS issues**: Backend is configured to allow frontend origin

### Useful Commands

```bash
# Check what's running on ports
lsof -i :5173  # Frontend
lsof -i :8080  # Backend API
lsof -i :5432  # PostgreSQL

# View Docker logs
docker-compose -f backend/web-api/docker-compose.yml logs

# Reset everything
docker-compose -f backend/web-api/docker-compose.yml down -v
docker-compose -f backend/web-api/docker-compose.yml up -d
```

## 🎯 Next Steps

1. **Connect AI Service**: Integrate with your AI agents backend
2. **Add Real-time Features**: Implement WebSocket for live chat
3. **Enhance UI**: Customize the design and add more features
4. **Deploy**: Set up production deployment

## 📚 Documentation

- [Frontend README](frontend/README.md)
- [Backend README](backend/web-api/README.md)

## 🤝 Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Test both frontend and backend changes

---

**Happy coding! 🚀**
