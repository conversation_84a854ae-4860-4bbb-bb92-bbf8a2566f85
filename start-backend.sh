#!/bin/bash

# Script to start the backend services
echo "🚀 Starting ChatBot Backend Services..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Navigate to backend directory
cd backend/web-api

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found in backend/web-api directory"
    exit 1
fi

echo "📦 Starting PostgreSQL, Redis, and Web API..."

# Start the services
docker-compose up -d

# Wait a moment for services to start
echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
echo "🔍 Checking service status..."

if docker-compose ps | grep -q "Up"; then
    echo "✅ Backend services are running!"
    echo ""
    echo "📋 Service URLs:"
    echo "   🌐 Web API: http://localhost:8080"
    echo "   🗄️  PostgreSQL: localhost:5432"
    echo "   🔴 Redis: localhost:6379"
    echo "   🛠️  pgAdmin: http://localhost:5050"
    echo ""
    echo "🧪 Test the API:"
    echo "   curl http://localhost:8080/health"
    echo ""
    echo "📖 API Documentation:"
    echo "   Authentication: http://localhost:8080/api/v1/auth/*"
    echo "   Chat Messages: http://localhost:8080/api/v1/chat/messages"
else
    echo "❌ Some services failed to start. Check the logs:"
    echo "   docker-compose logs"
fi
