# AI Agents Backend

A generative AI backend service built with Go, following clean architecture principles.

## Features

- Clean Architecture with domain-driven design
- RESTful API with Echo framework
- PostgreSQL database with GORM
- JWT authentication integration
- Multiple AI provider support (OpenAI, Anthropic)
- Structured logging
- Comprehensive error handling
- Request validation

## Project Structure

```
backend/ai-agents/
├── cmd/
│   └── server/          # Application entry point
├── internal/
│   ├── application/     # Application layer (use cases, services)
│   ├── domain/          # Domain layer (entities, repositories)
│   ├── infrastructure/  # Infrastructure layer (database, AI providers, HTTP)
│   └── interface/       # Interface layer (handlers, middleware)
├── pkg/                 # Shared packages
│   ├── errors/          # Error handling utilities
│   ├── logger/          # Logging utilities
│   └── validator/       # Validation utilities
├── .env.example         # Environment variables template
├── go.mod              # Go module definition
└── README.md           # This file
```

## Getting Started

### Prerequisites

- Go 1.24.2 or later
- PostgreSQL database
- AI provider API keys (OpenAI, Anthropic)

### Installation

1. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

2. Update the `.env` file with your configuration

3. Install dependencies:
   ```bash
   go mod tidy
   ```

4. Build the application:
   ```bash
   go build ./cmd/server
   ```

5. Run the server:
   ```bash
   ./server
   ```

The server will start on the port specified in your `.env` file (default: 8081).

## API Endpoints

### Health Check
- `GET /health` - Service health status

### Planned Endpoints
- `POST /api/v1/chat` - Send chat messages to AI agents
- `GET /api/v1/conversations` - List user conversations
- `POST /api/v1/conversations` - Create new conversation
- `GET /api/v1/agents` - List available AI agents
- `POST /api/v1/agents` - Create custom AI agent

## Development

### Running Tests
```bash
go test ./...
```

### Code Generation
```bash
go generate ./...
```

## Architecture

This service follows clean architecture principles with clear separation of concerns:

- **Domain Layer**: Contains business entities and repository interfaces
- **Application Layer**: Contains use cases and application services
- **Infrastructure Layer**: Contains external dependencies (database, AI APIs)
- **Interface Layer**: Contains HTTP handlers and middleware

## Contributing

1. Follow the existing code structure and patterns
2. Write tests for new functionality
3. Update documentation as needed
4. Ensure all tests pass before submitting
