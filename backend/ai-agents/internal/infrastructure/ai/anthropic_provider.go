package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/go-auggie-trial/ai-agents/internal/application/service"
	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/go-auggie-trial/ai-agents/internal/domain/message"
	"github.com/go-auggie-trial/ai-agents/pkg/logger"
)

// AnthropicProvider implements the AIProvider interface for Anthropic
type AnthropicProvider struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	logger     *logger.Logger
}

// AnthropicRequest represents a request to Anthropic API
type AnthropicRequest struct {
	Model       string              `json:"model"`
	MaxTokens   int                 `json:"max_tokens"`
	Messages    []AnthropicMessage  `json:"messages"`
	System      string              `json:"system,omitempty"`
	Temperature *float32            `json:"temperature,omitempty"`
}

// AnthropicMessage represents a message in Anthropic format
type AnthropicMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// AnthropicResponse represents a response from Anthropic API
type AnthropicResponse struct {
	ID           string                `json:"id"`
	Type         string                `json:"type"`
	Role         string                `json:"role"`
	Content      []AnthropicContent    `json:"content"`
	Model        string                `json:"model"`
	StopReason   string                `json:"stop_reason"`
	StopSequence string                `json:"stop_sequence"`
	Usage        AnthropicUsage        `json:"usage"`
	Error        *AnthropicError       `json:"error,omitempty"`
}

// AnthropicContent represents content in Anthropic response
type AnthropicContent struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// AnthropicUsage represents token usage in Anthropic response
type AnthropicUsage struct {
	InputTokens  int `json:"input_tokens"`
	OutputTokens int `json:"output_tokens"`
}

// AnthropicError represents an error from Anthropic API
type AnthropicError struct {
	Type    string `json:"type"`
	Message string `json:"message"`
}

// NewAnthropicProvider creates a new Anthropic provider
func NewAnthropicProvider(apiKey string, logger *logger.Logger) *AnthropicProvider {
	return &AnthropicProvider{
		apiKey:     apiKey,
		baseURL:    "https://api.anthropic.com/v1",
		httpClient: &http.Client{},
		logger:     logger,
	}
}

// Chat implements AIProvider.Chat
func (p *AnthropicProvider) Chat(ctx context.Context, request *service.ChatRequest) (*service.ChatResponse, error) {
	// Separate system message from other messages
	var systemPrompt string
	var messages []AnthropicMessage

	for _, msg := range request.Messages {
		if msg.Role == message.RoleSystem {
			systemPrompt = msg.Content
		} else {
			messages = append(messages, AnthropicMessage{
				Role:    string(msg.Role),
				Content: msg.Content,
			})
		}
	}

	// Use system prompt from request if provided
	if request.SystemPrompt != "" {
		systemPrompt = request.SystemPrompt
	}

	maxTokens := request.MaxTokens
	if maxTokens == 0 {
		maxTokens = 1000 // Default for Anthropic
	}

	anthropicRequest := AnthropicRequest{
		Model:     request.Model,
		MaxTokens: maxTokens,
		Messages:  messages,
		System:    systemPrompt,
	}

	if request.Temperature > 0 {
		anthropicRequest.Temperature = &request.Temperature
	}

	// Marshal request
	requestBody, err := json.Marshal(anthropicRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", p.baseURL+"/messages", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-api-key", p.apiKey)
	httpReq.Header.Set("anthropic-version", "2023-06-01")

	// Send request
	resp, err := p.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Parse response
	var anthropicResp AnthropicResponse
	if err := json.Unmarshal(responseBody, &anthropicResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Handle errors
	if anthropicResp.Error != nil {
		return nil, fmt.Errorf("Anthropic API error: %s", anthropicResp.Error.Message)
	}

	if len(anthropicResp.Content) == 0 {
		return nil, fmt.Errorf("no content in response")
	}

	// Build response
	content := anthropicResp.Content[0].Text
	totalTokens := anthropicResp.Usage.InputTokens + anthropicResp.Usage.OutputTokens

	return &service.ChatResponse{
		Content:    content,
		TokenCount: totalTokens,
		Model:      anthropicResp.Model,
		Provider:   agent.ProviderAnthropic,
		Metadata: map[string]interface{}{
			"stop_reason":    anthropicResp.StopReason,
			"input_tokens":   anthropicResp.Usage.InputTokens,
			"output_tokens":  anthropicResp.Usage.OutputTokens,
		},
	}, nil
}

// ValidateModel implements AIProvider.ValidateModel
func (p *AnthropicProvider) ValidateModel(ctx context.Context, model string) error {
	supportedModels, err := p.GetSupportedModels(ctx)
	if err != nil {
		return err
	}

	for _, supportedModel := range supportedModels {
		if supportedModel == model {
			return nil
		}
	}

	return service.ErrModelNotSupported
}

// GetSupportedModels implements AIProvider.GetSupportedModels
func (p *AnthropicProvider) GetSupportedModels(ctx context.Context) ([]string, error) {
	// Common Anthropic models - in a real implementation, you might fetch this from the API
	return []string{
		"claude-3-5-sonnet-20241022",
		"claude-3-5-haiku-20241022",
		"claude-3-opus-20240229",
		"claude-3-sonnet-20240229",
		"claude-3-haiku-20240307",
	}, nil
}

// GetProvider implements AIProvider.GetProvider
func (p *AnthropicProvider) GetProvider() agent.Provider {
	return agent.ProviderAnthropic
}
