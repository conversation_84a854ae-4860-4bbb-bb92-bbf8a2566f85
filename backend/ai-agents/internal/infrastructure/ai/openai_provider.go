package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/go-auggie-trial/ai-agents/internal/application/service"
	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/go-auggie-trial/ai-agents/pkg/logger"
)

// OpenAIProvider implements the AIProvider interface for OpenAI
type OpenAIProvider struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	logger     *logger.Logger
}

// OpenAIRequest represents a request to OpenAI API
type OpenAIRequest struct {
	Model       string              `json:"model"`
	Messages    []OpenAIMessage     `json:"messages"`
	Temperature *float32            `json:"temperature,omitempty"`
	MaxTokens   *int                `json:"max_tokens,omitempty"`
	Stream      bool                `json:"stream"`
}

// OpenAIMessage represents a message in OpenAI format
type OpenAIMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// OpenAIResponse represents a response from OpenAI API
type OpenAIResponse struct {
	ID      string           `json:"id"`
	Object  string           `json:"object"`
	Created int64            `json:"created"`
	Model   string           `json:"model"`
	Choices []OpenAIChoice   `json:"choices"`
	Usage   OpenAIUsage      `json:"usage"`
	Error   *OpenAIError     `json:"error,omitempty"`
}

// OpenAIChoice represents a choice in OpenAI response
type OpenAIChoice struct {
	Index        int           `json:"index"`
	Message      OpenAIMessage `json:"message"`
	FinishReason string        `json:"finish_reason"`
}

// OpenAIUsage represents token usage in OpenAI response
type OpenAIUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// OpenAIError represents an error from OpenAI API
type OpenAIError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code"`
}

// NewOpenAIProvider creates a new OpenAI provider
func NewOpenAIProvider(apiKey string, logger *logger.Logger) *OpenAIProvider {
	return &OpenAIProvider{
		apiKey:     apiKey,
		baseURL:    "https://api.openai.com/v1",
		httpClient: &http.Client{},
		logger:     logger,
	}
}

// Chat implements AIProvider.Chat
func (p *OpenAIProvider) Chat(ctx context.Context, request *service.ChatRequest) (*service.ChatResponse, error) {
	// Convert to OpenAI format
	openAIMessages := make([]OpenAIMessage, len(request.Messages))
	for i, msg := range request.Messages {
		openAIMessages[i] = OpenAIMessage{
			Role:    string(msg.Role),
			Content: msg.Content,
		}
	}

	openAIRequest := OpenAIRequest{
		Model:    request.Model,
		Messages: openAIMessages,
		Stream:   false,
	}

	if request.Temperature > 0 {
		openAIRequest.Temperature = &request.Temperature
	}
	if request.MaxTokens > 0 {
		openAIRequest.MaxTokens = &request.MaxTokens
	}

	// Marshal request
	requestBody, err := json.Marshal(openAIRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", p.baseURL+"/chat/completions", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+p.apiKey)

	// Send request
	resp, err := p.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Parse response
	var openAIResp OpenAIResponse
	if err := json.Unmarshal(responseBody, &openAIResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Handle errors
	if openAIResp.Error != nil {
		return nil, fmt.Errorf("OpenAI API error: %s", openAIResp.Error.Message)
	}

	if len(openAIResp.Choices) == 0 {
		return nil, fmt.Errorf("no choices in response")
	}

	// Build response
	choice := openAIResp.Choices[0]
	return &service.ChatResponse{
		Content:    choice.Message.Content,
		TokenCount: openAIResp.Usage.TotalTokens,
		Model:      openAIResp.Model,
		Provider:   agent.ProviderOpenAI,
		Metadata: map[string]interface{}{
			"finish_reason":      choice.FinishReason,
			"prompt_tokens":      openAIResp.Usage.PromptTokens,
			"completion_tokens":  openAIResp.Usage.CompletionTokens,
		},
	}, nil
}

// ValidateModel implements AIProvider.ValidateModel
func (p *OpenAIProvider) ValidateModel(ctx context.Context, model string) error {
	supportedModels, err := p.GetSupportedModels(ctx)
	if err != nil {
		return err
	}

	for _, supportedModel := range supportedModels {
		if supportedModel == model {
			return nil
		}
	}

	return service.ErrModelNotSupported
}

// GetSupportedModels implements AIProvider.GetSupportedModels
func (p *OpenAIProvider) GetSupportedModels(ctx context.Context) ([]string, error) {
	// Common OpenAI models - in a real implementation, you might fetch this from the API
	return []string{
		"gpt-4",
		"gpt-4-turbo",
		"gpt-4o",
		"gpt-4o-mini",
		"gpt-3.5-turbo",
	}, nil
}

// GetProvider implements AIProvider.GetProvider
func (p *OpenAIProvider) GetProvider() agent.Provider {
	return agent.ProviderOpenAI
}
