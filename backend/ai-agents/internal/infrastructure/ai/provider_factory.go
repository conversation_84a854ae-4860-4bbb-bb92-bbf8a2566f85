package ai

import (
	"os"

	"github.com/go-auggie-trial/ai-agents/internal/application/service"
	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/go-auggie-trial/ai-agents/pkg/logger"
)

// ProviderFactory creates and manages AI providers
type ProviderFactory struct {
	logger *logger.Logger
}

// NewProviderFactory creates a new provider factory
func NewProviderFactory(logger *logger.Logger) *ProviderFactory {
	return &ProviderFactory{
		logger: logger,
	}
}

// CreateProviders creates all available AI providers based on environment configuration
func (f *ProviderFactory) CreateProviders() map[agent.Provider]service.AIProvider {
	providers := make(map[agent.Provider]service.AIProvider)

	// Create OpenAI provider if API key is available
	if openAIKey := os.Getenv("OPENAI_API_KEY"); openAIKey != "" {
		providers[agent.ProviderOpenAI] = NewOpenAIProvider(openA<PERSON><PERSON><PERSON>, f.logger)
		f.logger.Info("OpenAI provider initialized")
	} else {
		f.logger.Warn("OpenAI API key not found, OpenAI provider disabled")
	}

	// Create Anthropic provider if API key is available
	if anthropicKey := os.Getenv("ANTHROPIC_API_KEY"); anthropicKey != "" {
		providers[agent.ProviderAnthropic] = NewAnthropicProvider(anthropicKey, f.logger)
		f.logger.Info("Anthropic provider initialized")
	} else {
		f.logger.Warn("Anthropic API key not found, Anthropic provider disabled")
	}

	// TODO: Add more providers as needed
	// if localEndpoint := os.Getenv("LOCAL_AI_ENDPOINT"); localEndpoint != "" {
	//     providers[agent.ProviderLocal] = NewLocalProvider(localEndpoint, f.logger)
	//     f.logger.Info("Local AI provider initialized")
	// }

	if len(providers) == 0 {
		f.logger.Warn("No AI providers configured - service will have limited functionality")
	}

	return providers
}

// GetAvailableProviders returns a list of available providers
func (f *ProviderFactory) GetAvailableProviders() []agent.Provider {
	var providers []agent.Provider

	if os.Getenv("OPENAI_API_KEY") != "" {
		providers = append(providers, agent.ProviderOpenAI)
	}

	if os.Getenv("ANTHROPIC_API_KEY") != "" {
		providers = append(providers, agent.ProviderAnthropic)
	}

	// TODO: Add more providers as needed

	return providers
}

// ValidateProviderConfiguration checks if the required configuration is available for a provider
func (f *ProviderFactory) ValidateProviderConfiguration(provider agent.Provider) error {
	switch provider {
	case agent.ProviderOpenAI:
		if os.Getenv("OPENAI_API_KEY") == "" {
			return service.ErrInvalidAPIKey
		}
	case agent.ProviderAnthropic:
		if os.Getenv("ANTHROPIC_API_KEY") == "" {
			return service.ErrInvalidAPIKey
		}
	case agent.ProviderLocal:
		// TODO: Add local provider validation
		return service.ErrProviderNotSupported
	default:
		return service.ErrProviderNotSupported
	}

	return nil
}
