package server

import (
	"context"
	"net/http"

	"github.com/go-auggie-trial/ai-agents/internal/interface/middleware"
	"github.com/go-auggie-trial/ai-agents/pkg/logger"
	"github.com/labstack/echo/v4"
	echomiddleware "github.com/labstack/echo/v4/middleware"
	"gorm.io/gorm"
)

// Server represents the HTTP server
type Server struct {
	echo   *echo.Echo
	db     *gorm.DB
	logger *logger.Logger
}

// New creates a new HTTP server instance
func New(db *gorm.DB, logger *logger.Logger) *Server {
	e := echo.New()

	// Hide Echo banner
	e.HideBanner = true

	// Create server instance
	server := &Server{
		echo:   e,
		db:     db,
		logger: logger,
	}

	// Setup middleware
	server.setupMiddleware()

	// Setup routes
	server.setupRoutes()

	return server
}

// setupMiddleware configures middleware for the server
func (s *Server) setupMiddleware() {
	// CORS middleware
	s.echo.Use(echomiddleware.CORSWithConfig(echomiddleware.CORSConfig{
		AllowOrigins: []string{"*"},
		AllowMethods: []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodOptions},
		AllowHeaders: []string{"*"},
	}))

	// Request logging middleware
	s.echo.Use(echomiddleware.LoggerWithConfig(echomiddleware.LoggerConfig{
		Format: `{"time":"${time_rfc3339}","method":"${method}","uri":"${uri}","status":${status},"latency":"${latency_human}"}` + "\n",
	}))

	// Recovery middleware
	s.echo.Use(echomiddleware.Recover())

	// Request ID middleware
	s.echo.Use(echomiddleware.RequestID())

	// Custom error handler middleware
	s.echo.Use(middleware.ErrorHandler(s.logger))
}

// setupRoutes configures routes for the server
func (s *Server) setupRoutes() {
	// Health check endpoint
	s.echo.GET("/health", s.healthCheck)

	// API v1 routes
	v1 := s.echo.Group("/api/v1")

	// TODO: Initialize handlers and add routes
	// This will be completed when we wire up the dependencies

	_ = v1 // Prevent unused variable error for now
}

// healthCheck handles health check requests
func (s *Server) healthCheck(c echo.Context) error {
	return c.JSON(http.StatusOK, map[string]string{
		"status":  "ok",
		"service": "ai-agents",
	})
}

// Start starts the HTTP server
func (s *Server) Start(address string) error {
	return s.echo.Start(address)
}

// Shutdown gracefully shuts down the server
func (s *Server) Shutdown(ctx context.Context) error {
	return s.echo.Shutdown(ctx)
}
