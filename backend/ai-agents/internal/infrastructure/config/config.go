package config

import (
	"fmt"
	"os"
	"strconv"
	"time"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig   `json:"server"`
	Database DatabaseConfig `json:"database"`
	JWT      JWTConfig      `json:"jwt"`
	AI       AIConfig       `json:"ai"`
	Logging  LoggingConfig  `json:"logging"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port         string        `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	Name     string `json:"name"`
	SSLMode  string `json:"ssl_mode"`
}

// JWTConfig holds JWT configuration
type JWTConfig struct {
	SecretKey string `json:"secret_key"`
}

// AIConfig holds AI provider configuration
type AIConfig struct {
	OpenAIAPIKey    string `json:"openai_api_key"`
	AnthropicAPIKey string `json:"anthropic_api_key"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level string `json:"level"`
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	config := &Config{
		Server: ServerConfig{
			Port:         getEnvOrDefault("AI_AGENTS_PORT", "8081"),
			ReadTimeout:  parseDurationOrDefault(getEnvOrDefault("SERVER_READ_TIMEOUT", "30s")),
			WriteTimeout: parseDurationOrDefault(getEnvOrDefault("SERVER_WRITE_TIMEOUT", "30s")),
			IdleTimeout:  parseDurationOrDefault(getEnvOrDefault("SERVER_IDLE_TIMEOUT", "60s")),
		},
		Database: DatabaseConfig{
			Host:     getEnvOrDefault("DB_HOST", "localhost"),
			Port:     getEnvOrDefault("DB_PORT", "5432"),
			User:     getEnvOrDefault("DB_USER", "postgres"),
			Password: getEnvOrDefault("DB_PASSWORD", ""),
			Name:     getEnvOrDefault("DB_NAME", "ai_agents"),
			SSLMode:  getEnvOrDefault("DB_SSLMODE", "disable"),
		},
		JWT: JWTConfig{
			SecretKey: os.Getenv("JWT_SECRET"),
		},
		AI: AIConfig{
			OpenAIAPIKey:    os.Getenv("OPENAI_API_KEY"),
			AnthropicAPIKey: os.Getenv("ANTHROPIC_API_KEY"),
		},
		Logging: LoggingConfig{
			Level: getEnvOrDefault("LOG_LEVEL", "info"),
		},
	}

	// Validate required configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return config, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate server configuration
	if c.Server.Port == "" {
		return fmt.Errorf("server port is required")
	}

	// Validate port number
	if port, err := strconv.Atoi(c.Server.Port); err != nil || port < 1 || port > 65535 {
		return fmt.Errorf("invalid server port: %s", c.Server.Port)
	}

	// Validate database configuration
	if c.Database.Host == "" {
		return fmt.Errorf("database host is required")
	}
	if c.Database.Port == "" {
		return fmt.Errorf("database port is required")
	}
	if c.Database.User == "" {
		return fmt.Errorf("database user is required")
	}
	if c.Database.Name == "" {
		return fmt.Errorf("database name is required")
	}

	// Validate JWT configuration
	if c.JWT.SecretKey == "" {
		return fmt.Errorf("JWT secret key is required")
	}

	// Validate AI configuration (at least one provider should be configured)
	if c.AI.OpenAIAPIKey == "" && c.AI.AnthropicAPIKey == "" {
		return fmt.Errorf("at least one AI provider API key is required (OpenAI or Anthropic)")
	}

	// Validate logging level
	validLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if !validLevels[c.Logging.Level] {
		return fmt.Errorf("invalid log level: %s (must be debug, info, warn, or error)", c.Logging.Level)
	}

	return nil
}

// GetDSN returns the database connection string
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		c.Host, c.Port, c.User, c.Password, c.Name, c.SSLMode)
}

// HasOpenAI returns true if OpenAI API key is configured
func (c *AIConfig) HasOpenAI() bool {
	return c.OpenAIAPIKey != ""
}

// HasAnthropic returns true if Anthropic API key is configured
func (c *AIConfig) HasAnthropic() bool {
	return c.AnthropicAPIKey != ""
}

// getEnvOrDefault returns environment variable value or default if not set
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// parseDurationOrDefault parses duration string or returns default
func parseDurationOrDefault(durationStr string) time.Duration {
	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		return 30 * time.Second // Default fallback
	}
	return duration
}
