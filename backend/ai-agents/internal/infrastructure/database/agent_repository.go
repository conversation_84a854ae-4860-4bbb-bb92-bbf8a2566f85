package database

import (
	"context"

	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AgentRepository implements the agent.Repository interface
type AgentRepository struct {
	db *gorm.DB
}

// NewAgentRepository creates a new agent repository
func NewAgentRepository(db *gorm.DB) agent.Repository {
	return &AgentRepository{db: db}
}

// Create implements agent.Repository.Create
func (r *AgentRepository) Create(ctx context.Context, agent *agent.Agent) error {
	return r.db.WithContext(ctx).Create(agent).Error
}

// GetByID implements agent.Repository.GetByID
func (r *AgentRepository) GetByID(ctx context.Context, id uuid.UUID) (*agent.Agent, error) {
	var a agent.Agent
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&a).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, agent.ErrAgentNotFound
		}
		return nil, err
	}
	return &a, nil
}

// GetByName implements agent.Repository.GetByName
func (r *AgentRepository) GetByName(ctx context.Context, name string, createdBy uuid.UUID) (*agent.Agent, error) {
	var a agent.Agent
	err := r.db.WithContext(ctx).Where("name = ? AND created_by = ?", name, createdBy).First(&a).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, agent.ErrAgentNotFound
		}
		return nil, err
	}
	return &a, nil
}

// List implements agent.Repository.List
func (r *AgentRepository) List(ctx context.Context, filter *agent.ListFilter) ([]*agent.Agent, error) {
	var agents []*agent.Agent
	
	query := r.db.WithContext(ctx).Model(&agent.Agent{})
	
	// Apply filters
	if filter.Provider != nil {
		query = query.Where("provider = ?", *filter.Provider)
	}
	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}
	if filter.IsPublic != nil {
		query = query.Where("is_public = ?", *filter.IsPublic)
	}
	if filter.CreatedBy != nil {
		query = query.Where("created_by = ?", *filter.CreatedBy)
	}
	if filter.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
	}
	
	// Apply pagination
	query = query.Limit(filter.Limit).Offset(filter.Offset)
	
	// Apply ordering
	query = query.Order("created_at DESC")
	
	err := query.Find(&agents).Error
	return agents, err
}

// Update implements agent.Repository.Update
func (r *AgentRepository) Update(ctx context.Context, agent *agent.Agent) error {
	return r.db.WithContext(ctx).Save(agent).Error
}

// Delete implements agent.Repository.Delete
func (r *AgentRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&agent.Agent{}, id).Error
}

// GetAccessibleAgents implements agent.Repository.GetAccessibleAgents
func (r *AgentRepository) GetAccessibleAgents(ctx context.Context, userID uuid.UUID, filter *agent.ListFilter) ([]*agent.Agent, error) {
	var agents []*agent.Agent
	
	query := r.db.WithContext(ctx).Model(&agent.Agent{})
	
	// Only get agents that are public or owned by the user
	query = query.Where("is_public = ? OR created_by = ?", true, userID)
	
	// Apply other filters
	if filter.Provider != nil {
		query = query.Where("provider = ?", *filter.Provider)
	}
	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}
	if filter.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
	}
	
	// Apply pagination
	query = query.Limit(filter.Limit).Offset(filter.Offset)
	
	// Apply ordering
	query = query.Order("created_at DESC")
	
	err := query.Find(&agents).Error
	return agents, err
}
