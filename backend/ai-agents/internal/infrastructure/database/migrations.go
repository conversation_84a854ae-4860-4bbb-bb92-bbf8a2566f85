package database

import (
	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/go-auggie-trial/ai-agents/internal/domain/conversation"
	"github.com/go-auggie-trial/ai-agents/internal/domain/message"
	"gorm.io/gorm"
)

// RunMigrations runs all database migrations
func RunMigrations(db *gorm.DB) error {
	return db.AutoMigrate(
		&agent.Agent{},
		&conversation.Conversation{},
		&message.Message{},
	)
}
