package database

import (
	"context"

	"github.com/go-auggie-trial/ai-agents/internal/domain/message"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MessageRepository implements the message.Repository interface
type MessageRepository struct {
	db *gorm.DB
}

// NewMessageRepository creates a new message repository
func NewMessageRepository(db *gorm.DB) message.Repository {
	return &MessageRepository{db: db}
}

// Create implements message.Repository.Create
func (r *MessageRepository) Create(ctx context.Context, msg *message.Message) error {
	return r.db.WithContext(ctx).Create(msg).Error
}

// CreateBatch implements message.Repository.CreateBatch
func (r *MessageRepository) CreateBatch(ctx context.Context, messages []*message.Message) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, msg := range messages {
			if err := tx.Create(msg).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetByID implements message.Repository.GetByID
func (r *MessageRepository) GetByID(ctx context.Context, id uuid.UUID) (*message.Message, error) {
	var msg message.Message
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&msg).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, message.ErrMessageNotFound
		}
		return nil, err
	}
	return &msg, nil
}

// GetByConversationID implements message.Repository.GetByConversationID
func (r *MessageRepository) GetByConversationID(ctx context.Context, conversationID uuid.UUID, filter *message.ListFilter) ([]*message.Message, error) {
	var messages []*message.Message
	
	query := r.db.WithContext(ctx).Model(&message.Message{}).Where("conversation_id = ?", conversationID)
	
	// Apply filters
	if filter.Role != nil {
		query = query.Where("role = ?", *filter.Role)
	}
	
	// Apply sorting
	sortBy := filter.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	
	if filter.SortDesc {
		query = query.Order(sortBy + " DESC")
	} else {
		query = query.Order(sortBy + " ASC")
	}
	
	// Apply pagination
	query = query.Limit(filter.Limit).Offset(filter.Offset)
	
	err := query.Find(&messages).Error
	return messages, err
}

// GetConversationHistory implements message.Repository.GetConversationHistory
func (r *MessageRepository) GetConversationHistory(ctx context.Context, conversationID uuid.UUID, limit int) ([]*message.Message, error) {
	var messages []*message.Message
	
	err := r.db.WithContext(ctx).
		Where("conversation_id = ?", conversationID).
		Order("created_at ASC").
		Limit(limit).
		Find(&messages).Error
	
	return messages, err
}

// Update implements message.Repository.Update
func (r *MessageRepository) Update(ctx context.Context, msg *message.Message) error {
	return r.db.WithContext(ctx).Save(msg).Error
}

// Delete implements message.Repository.Delete
func (r *MessageRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&message.Message{}, id).Error
}

// DeleteByConversationID implements message.Repository.DeleteByConversationID
func (r *MessageRepository) DeleteByConversationID(ctx context.Context, conversationID uuid.UUID) error {
	return r.db.WithContext(ctx).Where("conversation_id = ?", conversationID).Delete(&message.Message{}).Error
}

// Count implements message.Repository.Count
func (r *MessageRepository) Count(ctx context.Context, conversationID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&message.Message{}).
		Where("conversation_id = ?", conversationID).
		Count(&count).Error
	return count, err
}

// GetTokenCount implements message.Repository.GetTokenCount
func (r *MessageRepository) GetTokenCount(ctx context.Context, conversationID uuid.UUID) (int, error) {
	var result struct {
		TotalTokens int
	}
	
	err := r.db.WithContext(ctx).Model(&message.Message{}).
		Select("COALESCE(SUM(token_count), 0) as total_tokens").
		Where("conversation_id = ?", conversationID).
		Scan(&result).Error
	
	return result.TotalTokens, err
}
