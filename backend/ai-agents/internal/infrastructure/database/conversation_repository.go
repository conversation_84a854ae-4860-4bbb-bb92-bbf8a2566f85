package database

import (
	"context"

	"github.com/go-auggie-trial/ai-agents/internal/domain/conversation"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ConversationRepository implements the conversation.Repository interface
type ConversationRepository struct {
	db *gorm.DB
}

// NewConversationRepository creates a new conversation repository
func NewConversationRepository(db *gorm.DB) conversation.Repository {
	return &ConversationRepository{db: db}
}

// Create implements conversation.Repository.Create
func (r *ConversationRepository) Create(ctx context.Context, conv *conversation.Conversation) error {
	return r.db.WithContext(ctx).Create(conv).Error
}

// GetByID implements conversation.Repository.GetByID
func (r *ConversationRepository) GetByID(ctx context.Context, id uuid.UUID) (*conversation.Conversation, error) {
	var conv conversation.Conversation
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&conv).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, conversation.ErrConversationNotFound
		}
		return nil, err
	}
	return &conv, nil
}

// GetByUserID implements conversation.Repository.GetByUserID
func (r *ConversationRepository) GetByUserID(ctx context.Context, userID uuid.UUID, filter *conversation.ListFilter) ([]*conversation.Conversation, error) {
	var conversations []*conversation.Conversation
	
	query := r.db.WithContext(ctx).Model(&conversation.Conversation{}).Where("user_id = ?", userID)
	
	// Apply filters
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.AgentID != nil {
		query = query.Where("agent_id = ?", *filter.AgentID)
	}
	if filter.Search != "" {
		query = query.Where("title ILIKE ?", "%"+filter.Search+"%")
	}
	
	// Apply sorting
	sortBy := filter.SortBy
	if sortBy == "" {
		sortBy = "updated_at"
	}
	
	if filter.SortDesc {
		query = query.Order(sortBy + " DESC")
	} else {
		query = query.Order(sortBy + " ASC")
	}
	
	// Apply pagination
	query = query.Limit(filter.Limit).Offset(filter.Offset)
	
	err := query.Find(&conversations).Error
	return conversations, err
}

// Update implements conversation.Repository.Update
func (r *ConversationRepository) Update(ctx context.Context, conv *conversation.Conversation) error {
	return r.db.WithContext(ctx).Save(conv).Error
}

// Delete implements conversation.Repository.Delete (soft delete)
func (r *ConversationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&conversation.Conversation{}).
		Where("id = ?", id).
		Update("status", conversation.StatusDeleted).Error
}

// HardDelete implements conversation.Repository.HardDelete
func (r *ConversationRepository) HardDelete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&conversation.Conversation{}, id).Error
}

// Count implements conversation.Repository.Count
func (r *ConversationRepository) Count(ctx context.Context, userID uuid.UUID, filter *conversation.ListFilter) (int64, error) {
	var count int64
	
	query := r.db.WithContext(ctx).Model(&conversation.Conversation{}).Where("user_id = ?", userID)
	
	// Apply filters
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.AgentID != nil {
		query = query.Where("agent_id = ?", *filter.AgentID)
	}
	if filter.Search != "" {
		query = query.Where("title ILIKE ?", "%"+filter.Search+"%")
	}
	
	err := query.Count(&count).Error
	return count, err
}
