package handler

import (
	"net/http"
	"strconv"

	"github.com/go-auggie-trial/ai-agents/internal/application/usecase"
	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/go-auggie-trial/ai-agents/pkg/errors"
	"github.com/go-auggie-trial/ai-agents/pkg/validator"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// AgentHandler handles agent-related HTTP requests
type AgentHandler struct {
	agentUseCase *usecase.AgentUseCase
	validator    *validator.Validator
}

// NewAgentHandler creates a new agent handler
func NewAgentHandler(agentUseCase *usecase.AgentUseCase, validator *validator.Validator) *AgentHandler {
	return &AgentHandler{
		agentUseCase: agentUseCase,
		validator:    validator,
	}
}

// CreateAgent handles POST /agents
func (h *AgentHandler) CreateAgent(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	var req agent.AgentRequest
	if err := c.Bind(&req); err != nil {
		return errors.BadRequest("Invalid request body", err)
	}

	if err := h.validator.Validate(&req); err != nil {
		return errors.BadRequest("Validation failed", err)
	}

	agentEntity, err := h.agentUseCase.CreateAgent(c.Request().Context(), userID, &req)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusCreated, agentEntity.ToResponse())
}

// GetAgent handles GET /agents/:id
func (h *AgentHandler) GetAgent(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	agentID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid agent ID", err)
	}

	agentEntity, err := h.agentUseCase.GetAgent(c.Request().Context(), userID, agentID)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, agentEntity.ToResponse())
}

// ListAgents handles GET /agents
func (h *AgentHandler) ListAgents(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	filter := parseAgentListFilter(c)
	
	agents, err := h.agentUseCase.ListAgents(c.Request().Context(), userID, filter)
	if err != nil {
		return handleUseCaseError(err)
	}

	responses := make([]*agent.AgentResponse, len(agents))
	for i, a := range agents {
		responses[i] = a.ToResponse()
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"agents": responses,
		"total":  len(responses),
	})
}

// ListMyAgents handles GET /agents/my
func (h *AgentHandler) ListMyAgents(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	filter := parseAgentListFilter(c)
	
	agents, err := h.agentUseCase.ListMyAgents(c.Request().Context(), userID, filter)
	if err != nil {
		return handleUseCaseError(err)
	}

	responses := make([]*agent.AgentResponse, len(agents))
	for i, a := range agents {
		responses[i] = a.ToResponse()
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"agents": responses,
		"total":  len(responses),
	})
}

// UpdateAgent handles PUT /agents/:id
func (h *AgentHandler) UpdateAgent(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	agentID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid agent ID", err)
	}

	var req agent.AgentRequest
	if err := c.Bind(&req); err != nil {
		return errors.BadRequest("Invalid request body", err)
	}

	if err := h.validator.Validate(&req); err != nil {
		return errors.BadRequest("Validation failed", err)
	}

	agentEntity, err := h.agentUseCase.UpdateAgent(c.Request().Context(), userID, agentID, &req)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, agentEntity.ToResponse())
}

// DeleteAgent handles DELETE /agents/:id
func (h *AgentHandler) DeleteAgent(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	agentID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid agent ID", err)
	}

	if err := h.agentUseCase.DeleteAgent(c.Request().Context(), userID, agentID); err != nil {
		return handleUseCaseError(err)
	}

	return c.NoContent(http.StatusNoContent)
}

// ToggleAgentStatus handles POST /agents/:id/toggle
func (h *AgentHandler) ToggleAgentStatus(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	agentID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid agent ID", err)
	}

	agentEntity, err := h.agentUseCase.ToggleAgentStatus(c.Request().Context(), userID, agentID)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, agentEntity.ToResponse())
}

// GetSupportedModels handles GET /agents/models/:provider
func (h *AgentHandler) GetSupportedModels(c echo.Context) error {
	providerStr := c.Param("provider")
	provider := agent.Provider(providerStr)
	
	if !provider.IsValid() {
		return errors.BadRequest("Invalid provider", nil)
	}

	models, err := h.agentUseCase.GetSupportedModels(c.Request().Context(), provider)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"provider": provider,
		"models":   models,
	})
}

// parseAgentListFilter parses query parameters into agent list filter
func parseAgentListFilter(c echo.Context) *agent.ListFilter {
	filter := agent.DefaultListFilter()

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			filter.Limit = limit
		}
	}

	if offsetStr := c.QueryParam("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			filter.Offset = offset
		}
	}

	if providerStr := c.QueryParam("provider"); providerStr != "" {
		provider := agent.Provider(providerStr)
		if provider.IsValid() {
			filter.Provider = &provider
		}
	}

	if activeStr := c.QueryParam("active"); activeStr != "" {
		if active, err := strconv.ParseBool(activeStr); err == nil {
			filter.IsActive = &active
		}
	}

	if publicStr := c.QueryParam("public"); publicStr != "" {
		if public, err := strconv.ParseBool(publicStr); err == nil {
			filter.IsPublic = &public
		}
	}

	if search := c.QueryParam("search"); search != "" {
		filter.Search = search
	}

	return filter
}
