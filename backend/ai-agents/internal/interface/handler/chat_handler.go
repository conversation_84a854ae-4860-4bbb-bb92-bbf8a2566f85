package handler

import (
	"net/http"
	"strconv"

	"github.com/go-auggie-trial/ai-agents/internal/application/usecase"
	"github.com/go-auggie-trial/ai-agents/internal/domain/message"
	"github.com/go-auggie-trial/ai-agents/pkg/errors"
	"github.com/go-auggie-trial/ai-agents/pkg/validator"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// <PERSON><PERSON><PERSON><PERSON><PERSON> handles chat-related HTTP requests
type ChatHandler struct {
	chatUseCase *usecase.ChatUseCase
	validator   *validator.Validator
}

// NewChatHandler creates a new chat handler
func NewChatHandler(chatUseCase *usecase.ChatUseCase, validator *validator.Validator) *ChatHandler {
	return &ChatHandler{
		chatUseCase: chatUseCase,
		validator:   validator,
	}
}

// SendMessage handles POST /chat
func (h *<PERSON>t<PERSON>and<PERSON>) SendMessage(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	var req message.ChatRequest
	if err := c.Bind(&req); err != nil {
		return errors.BadRequest("Invalid request body", err)
	}

	if err := h.validator.Validate(&req); err != nil {
		return errors.BadRequest("Validation failed", err)
	}

	response, err := h.chatUseCase.SendMessage(c.Request().Context(), userID, &req)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, response)
}

// GetMessages handles GET /conversations/:id/messages
func (h *ChatHandler) GetMessages(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	conversationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid conversation ID", err)
	}

	filter := parseMessageListFilter(c)
	
	messages, err := h.chatUseCase.GetMessages(c.Request().Context(), userID, conversationID, filter)
	if err != nil {
		return handleUseCaseError(err)
	}

	responses := make([]*message.MessageResponse, len(messages))
	for i, msg := range messages {
		responses[i] = msg.ToResponse()
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"messages": responses,
		"total":    len(responses),
		"limit":    filter.Limit,
		"offset":   filter.Offset,
	})
}

// GetMessage handles GET /messages/:id
func (h *ChatHandler) GetMessage(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	messageID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid message ID", err)
	}

	msg, err := h.chatUseCase.GetMessage(c.Request().Context(), userID, messageID)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, msg.ToResponse())
}

// DeleteMessage handles DELETE /messages/:id
func (h *ChatHandler) DeleteMessage(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	messageID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid message ID", err)
	}

	if err := h.chatUseCase.DeleteMessage(c.Request().Context(), userID, messageID); err != nil {
		return handleUseCaseError(err)
	}

	return c.NoContent(http.StatusNoContent)
}

// GetConversationTokenCount handles GET /conversations/:id/tokens
func (h *ChatHandler) GetConversationTokenCount(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	conversationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid conversation ID", err)
	}

	tokenCount, err := h.chatUseCase.GetConversationTokenCount(c.Request().Context(), userID, conversationID)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"conversation_id": conversationID,
		"token_count":     tokenCount,
	})
}

// ClearConversation handles DELETE /conversations/:id/messages
func (h *ChatHandler) ClearConversation(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	conversationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid conversation ID", err)
	}

	if err := h.chatUseCase.ClearConversation(c.Request().Context(), userID, conversationID); err != nil {
		return handleUseCaseError(err)
	}

	return c.NoContent(http.StatusNoContent)
}

// parseMessageListFilter parses query parameters into message list filter
func parseMessageListFilter(c echo.Context) *message.ListFilter {
	filter := message.DefaultListFilter()

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			filter.Limit = limit
		}
	}

	if offsetStr := c.QueryParam("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			filter.Offset = offset
		}
	}

	if roleStr := c.QueryParam("role"); roleStr != "" {
		role := message.Role(roleStr)
		if role.IsValid() {
			filter.Role = &role
		}
	}

	if sortBy := c.QueryParam("sort_by"); sortBy != "" {
		filter.SortBy = sortBy
	}

	if sortDescStr := c.QueryParam("sort_desc"); sortDescStr != "" {
		if sortDesc, err := strconv.ParseBool(sortDescStr); err == nil {
			filter.SortDesc = sortDesc
		}
	}

	return filter
}
