package handler

import (
	"net/http"

	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/go-auggie-trial/ai-agents/internal/domain/conversation"
	"github.com/go-auggie-trial/ai-agents/internal/domain/message"
	"github.com/go-auggie-trial/ai-agents/pkg/errors"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// getUserIDFromContext extracts user ID from the request context
// This assumes JWT middleware has already validated the token and set the user ID
func getUserIDFromContext(c echo.Context) uuid.UUID {
	userID := c.Get("user_id")
	if userID == nil {
		// This should never happen if JWT middleware is working properly
		return uuid.Nil
	}

	id, ok := userID.(uuid.UUID)
	if !ok {
		return uuid.Nil
	}

	return id
}

// handleUseCaseError converts use case errors to appropriate HTTP errors
func handleUseCaseError(err error) error {
	switch err {
	// Agent errors
	case agent.ErrAgentNotFound:
		return errors.NotFound("Agent not found", err)
	case agent.ErrAgentNotAccessible:
		return errors.Forbidden("Agent not accessible", err)
	case agent.ErrDuplicateAgent:
		return errors.BadRequest("Agent with this name already exists", err)
	case agent.ErrInvalidName:
		return errors.BadRequest("Invalid agent name", err)
	case agent.ErrInvalidProvider:
		return errors.BadRequest("Invalid provider", err)
	case agent.ErrInvalidModel:
		return errors.BadRequest("Invalid model", err)
	case agent.ErrInvalidTemperature:
		return errors.BadRequest("Invalid temperature", err)
	case agent.ErrInvalidMaxTokens:
		return errors.BadRequest("Invalid max tokens", err)
	case agent.ErrInvalidCreatedBy:
		return errors.BadRequest("Invalid created by", err)

	// Conversation errors
	case conversation.ErrConversationNotFound:
		return errors.NotFound("Conversation not found", err)
	case conversation.ErrUnauthorizedAccess:
		return errors.Forbidden("Unauthorized access to conversation", err)
	case conversation.ErrConversationDeleted:
		return errors.BadRequest("Conversation is deleted", err)
	case conversation.ErrInvalidTitle:
		return errors.BadRequest("Invalid conversation title", err)
	case conversation.ErrInvalidUserID:
		return errors.BadRequest("Invalid user ID", err)
	case conversation.ErrInvalidAgentID:
		return errors.BadRequest("Invalid agent ID", err)
	case conversation.ErrInvalidStatus:
		return errors.BadRequest("Invalid conversation status", err)

	// Message errors
	case message.ErrMessageNotFound:
		return errors.NotFound("Message not found", err)
	case message.ErrInvalidConversationID:
		return errors.BadRequest("Invalid conversation ID", err)
	case message.ErrInvalidRole:
		return errors.BadRequest("Invalid message role", err)
	case message.ErrInvalidContent:
		return errors.BadRequest("Invalid message content", err)
	case message.ErrMessageTooLong:
		return errors.BadRequest("Message content too long", err)
	case message.ErrEmptyMessage:
		return errors.BadRequest("Message content cannot be empty", err)

	// Default to internal server error
	default:
		return errors.InternalServer("Internal server error", err)
	}
}

// HealthResponse represents a health check response
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
	Version string `json:"version,omitempty"`
}

// HealthCheck handles health check requests
func HealthCheck(c echo.Context) error {
	return c.JSON(http.StatusOK, HealthResponse{
		Status:  "ok",
		Service: "ai-agents",
		Version: "1.0.0",
	})
}
