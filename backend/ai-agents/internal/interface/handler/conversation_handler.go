package handler

import (
	"net/http"
	"strconv"

	"github.com/go-auggie-trial/ai-agents/internal/application/usecase"
	"github.com/go-auggie-trial/ai-agents/internal/domain/conversation"
	"github.com/go-auggie-trial/ai-agents/pkg/errors"
	"github.com/go-auggie-trial/ai-agents/pkg/validator"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// ConversationHandler handles conversation-related HTTP requests
type ConversationHandler struct {
	conversationUseCase *usecase.ConversationUseCase
	validator           *validator.Validator
}

// NewConversationHandler creates a new conversation handler
func NewConversationHandler(conversationUseCase *usecase.ConversationUseCase, validator *validator.Validator) *ConversationHandler {
	return &ConversationHandler{
		conversationUseCase: conversationUseCase,
		validator:           validator,
	}
}

// CreateConversation handles POST /conversations
func (h *ConversationHandler) CreateConversation(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	var req conversation.ConversationRequest
	if err := c.Bind(&req); err != nil {
		return errors.BadRequest("Invalid request body", err)
	}

	if err := h.validator.Validate(&req); err != nil {
		return errors.BadRequest("Validation failed", err)
	}

	conv, err := h.conversationUseCase.CreateConversation(c.Request().Context(), userID, &req)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusCreated, conv.ToResponse())
}

// GetConversation handles GET /conversations/:id
func (h *ConversationHandler) GetConversation(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	conversationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid conversation ID", err)
	}

	conv, err := h.conversationUseCase.GetConversation(c.Request().Context(), userID, conversationID)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, conv.ToResponse())
}

// ListConversations handles GET /conversations
func (h *ConversationHandler) ListConversations(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	filter := parseConversationListFilter(c)
	
	conversations, err := h.conversationUseCase.ListConversations(c.Request().Context(), userID, filter)
	if err != nil {
		return handleUseCaseError(err)
	}

	responses := make([]*conversation.ConversationResponse, len(conversations))
	for i, conv := range conversations {
		responses[i] = conv.ToResponse()
	}

	// Get total count for pagination
	total, err := h.conversationUseCase.GetConversationCount(c.Request().Context(), userID, filter)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"conversations": responses,
		"total":         total,
		"limit":         filter.Limit,
		"offset":        filter.Offset,
	})
}

// UpdateConversation handles PUT /conversations/:id
func (h *ConversationHandler) UpdateConversation(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	conversationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid conversation ID", err)
	}

	var req conversation.UpdateConversationRequest
	if err := c.Bind(&req); err != nil {
		return errors.BadRequest("Invalid request body", err)
	}

	if err := h.validator.Validate(&req); err != nil {
		return errors.BadRequest("Validation failed", err)
	}

	conv, err := h.conversationUseCase.UpdateConversation(c.Request().Context(), userID, conversationID, &req)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, conv.ToResponse())
}

// DeleteConversation handles DELETE /conversations/:id
func (h *ConversationHandler) DeleteConversation(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	conversationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid conversation ID", err)
	}

	if err := h.conversationUseCase.DeleteConversation(c.Request().Context(), userID, conversationID); err != nil {
		return handleUseCaseError(err)
	}

	return c.NoContent(http.StatusNoContent)
}

// ArchiveConversation handles POST /conversations/:id/archive
func (h *ConversationHandler) ArchiveConversation(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	conversationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid conversation ID", err)
	}

	conv, err := h.conversationUseCase.ArchiveConversation(c.Request().Context(), userID, conversationID)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, conv.ToResponse())
}

// RestoreConversation handles POST /conversations/:id/restore
func (h *ConversationHandler) RestoreConversation(c echo.Context) error {
	userID := getUserIDFromContext(c)
	
	conversationID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		return errors.BadRequest("Invalid conversation ID", err)
	}

	conv, err := h.conversationUseCase.RestoreConversation(c.Request().Context(), userID, conversationID)
	if err != nil {
		return handleUseCaseError(err)
	}

	return c.JSON(http.StatusOK, conv.ToResponse())
}

// parseConversationListFilter parses query parameters into conversation list filter
func parseConversationListFilter(c echo.Context) *conversation.ListFilter {
	filter := conversation.DefaultListFilter()

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			filter.Limit = limit
		}
	}

	if offsetStr := c.QueryParam("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			filter.Offset = offset
		}
	}

	if statusStr := c.QueryParam("status"); statusStr != "" {
		status := conversation.Status(statusStr)
		if status.IsValid() {
			filter.Status = &status
		}
	}

	if agentIDStr := c.QueryParam("agent_id"); agentIDStr != "" {
		if agentID, err := uuid.Parse(agentIDStr); err == nil {
			filter.AgentID = &agentID
		}
	}

	if search := c.QueryParam("search"); search != "" {
		filter.Search = search
	}

	if sortBy := c.QueryParam("sort_by"); sortBy != "" {
		filter.SortBy = sortBy
	}

	if sortDescStr := c.QueryParam("sort_desc"); sortDescStr != "" {
		if sortDesc, err := strconv.ParseBool(sortDescStr); err == nil {
			filter.SortDesc = sortDesc
		}
	}

	return filter
}
