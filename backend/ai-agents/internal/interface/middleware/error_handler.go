package middleware

import (
	"net/http"

	"github.com/go-auggie-trial/ai-agents/pkg/errors"
	"github.com/go-auggie-trial/ai-agents/pkg/logger"
	"github.com/labstack/echo/v4"
)

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string      `json:"error"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

// ErrorHandler returns a middleware that handles errors
func ErrorHandler(logger *logger.Logger) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			err := next(c)
			if err == nil {
				return nil
			}

			// Handle different types of errors
			switch e := err.(type) {
			case *errors.AppError:
				return handleAppError(c, e, logger)
			case *echo.HTTPError:
				return handleEchoError(c, e, logger)
			default:
				return handleGenericError(c, err, logger)
			}
		}
	}
}

// handleAppError handles application-specific errors
func handleAppError(c echo.Context, err *errors.AppError, logger *logger.Logger) error {
	logger.Error("Application error",
		"error", err.Error(),
		"code", err.Code,
		"path", c.Request().URL.Path,
		"method", c.Request().Method,
	)

	response := ErrorResponse{
		Error:   http.StatusText(err.Code),
		Message: err.Message,
	}

	return c.JSON(err.Code, response)
}

// handleEchoError handles Echo framework errors
func handleEchoError(c echo.Context, err *echo.HTTPError, logger *logger.Logger) error {
	code := err.Code
	message := err.Message

	if message == nil {
		message = http.StatusText(code)
	}

	logger.Error("Echo error",
		"error", err.Error(),
		"code", code,
		"path", c.Request().URL.Path,
		"method", c.Request().Method,
	)

	response := ErrorResponse{
		Error:   http.StatusText(code),
		Message: message.(string),
	}

	return c.JSON(code, response)
}

// handleGenericError handles generic errors
func handleGenericError(c echo.Context, err error, logger *logger.Logger) error {
	logger.Error("Internal server error",
		"error", err.Error(),
		"path", c.Request().URL.Path,
		"method", c.Request().Method,
	)

	response := ErrorResponse{
		Error:   "Internal Server Error",
		Message: "An unexpected error occurred",
	}

	return c.JSON(http.StatusInternalServerError, response)
}
