package middleware

import (
	"os"
	"strings"

	"github.com/go-auggie-trial/ai-agents/pkg/errors"
	"github.com/go-auggie-trial/ai-agents/pkg/logger"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// AuthMiddleware handles JWT authentication
type AuthMiddleware struct {
	secretKey []byte
	logger    *logger.Logger
}

// Claims represents JWT claims compatible with web-api
type Claims struct {
	UserID    uuid.UUID `json:"user_id"`
	Email     string    `json:"email"`
	Username  string    `json:"username"`
	Role      string    `json:"role"`
	IssuedAt  int64     `json:"iat"`
	ExpiresAt int64     `json:"exp"`
}

// NewAuthMiddleware creates a new AuthMiddleware
func NewAuthMiddleware(logger *logger.Logger) *AuthMiddleware {
	secretKey := os.Getenv("JWT_SECRET")
	if secretKey == "" {
		logger.Fatal("JWT_SECRET environment variable is required")
	}

	return &AuthMiddleware{
		secretKey: []byte(secretKey),
		logger:    logger,
	}
}

// JWTAuth middleware validates JWT tokens
func (m *AuthMiddleware) JWTAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get token from Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return errors.Unauthorized("Authorization header is required", nil)
			}

			// Check if header starts with "Bearer "
			if !strings.HasPrefix(authHeader, "Bearer ") {
				return errors.Unauthorized("Authorization header must start with 'Bearer '", nil)
			}

			// Extract token
			tokenString := strings.TrimPrefix(authHeader, "Bearer ")
			if tokenString == "" {
				return errors.Unauthorized("Token is required", nil)
			}

			// Validate token
			claims, err := m.validateToken(tokenString)
			if err != nil {
				return errors.Unauthorized("Invalid token", err)
			}

			// Set user claims in context
			c.Set("user_id", claims.UserID)
			c.Set("user_email", claims.Email)
			c.Set("user_username", claims.Username)
			c.Set("user_role", claims.Role)

			return next(c)
		}
	}
}

// OptionalAuth middleware validates JWT tokens but doesn't require them
func (m *AuthMiddleware) OptionalAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get token from Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return next(c)
			}

			// Check if header starts with "Bearer "
			if !strings.HasPrefix(authHeader, "Bearer ") {
				return next(c)
			}

			// Extract token
			tokenString := strings.TrimPrefix(authHeader, "Bearer ")
			if tokenString == "" {
				return next(c)
			}

			// Validate token
			claims, err := m.validateToken(tokenString)
			if err == nil {
				// Set user claims in context if token is valid
				c.Set("user_id", claims.UserID)
				c.Set("user_email", claims.Email)
				c.Set("user_username", claims.Username)
				c.Set("user_role", claims.Role)
			}

			return next(c)
		}
	}
}

// AdminOnly middleware ensures only admin users can access the endpoint
func (m *AuthMiddleware) AdminOnly() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userRole := c.Get("user_role")
			if userRole == nil {
				return errors.Unauthorized("User role not found in context", nil)
			}

			role, ok := userRole.(string)
			if !ok || role != "admin" {
				return errors.Forbidden("Admin access required", nil)
			}

			return next(c)
		}
	}
}

// validateToken validates a JWT token and returns claims
func (m *AuthMiddleware) validateToken(tokenString string) (*Claims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.ErrInvalidToken
		}
		return m.secretKey, nil
	})

	if err != nil {
		return nil, errors.ErrInvalidToken
	}

	if !token.Valid {
		return nil, errors.ErrInvalidToken
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	// Extract claims
	userIDStr, ok := claims["user_id"].(string)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, errors.ErrInvalidToken
	}

	email, ok := claims["email"].(string)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	username, ok := claims["username"].(string)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	role, ok := claims["role"].(string)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	iat, ok := claims["iat"].(float64)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	exp, ok := claims["exp"].(float64)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	return &Claims{
		UserID:    userID,
		Email:     email,
		Username:  username,
		Role:      role,
		IssuedAt:  int64(iat),
		ExpiresAt: int64(exp),
	}, nil
}
