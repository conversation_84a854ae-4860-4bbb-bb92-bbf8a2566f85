package message

import "errors"

// Message domain errors
var (
	ErrMessageNotFound        = errors.New("message not found")
	ErrInvalidConversationID  = errors.New("invalid conversation ID")
	ErrInvalidRole            = errors.New("invalid message role")
	ErrInvalidContent         = errors.New("invalid message content")
	ErrMessageTooLong         = errors.New("message content too long")
	ErrEmptyMessage           = errors.New("message content cannot be empty")
)
