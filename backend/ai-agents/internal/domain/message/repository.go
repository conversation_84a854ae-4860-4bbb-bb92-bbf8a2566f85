package message

import (
	"context"

	"github.com/google/uuid"
)

// Repository defines the interface for message data operations
type Repository interface {
	// Create creates a new message
	Create(ctx context.Context, message *Message) error

	// CreateBatch creates multiple messages in a single transaction
	CreateBatch(ctx context.Context, messages []*Message) error

	// GetByID retrieves a message by ID
	GetByID(ctx context.Context, id uuid.UUID) (*Message, error)

	// GetByConversationID retrieves messages for a specific conversation
	GetByConversationID(ctx context.Context, conversationID uuid.UUID, filter *ListFilter) ([]*Message, error)

	// GetConversationHistory retrieves conversation history with context window
	GetConversationHistory(ctx context.Context, conversationID uuid.UUID, limit int) ([]*Message, error)

	// Update updates an existing message
	Update(ctx context.Context, message *Message) error

	// Delete deletes a message by ID
	Delete(ctx context.Context, id uuid.UUID) error

	// DeleteByConversationID deletes all messages in a conversation
	DeleteByConversationID(ctx context.Context, conversationID uuid.UUID) error

	// Count returns the total count of messages in a conversation
	Count(ctx context.Context, conversationID uuid.UUID) (int64, error)

	// GetTokenCount returns the total token count for a conversation
	GetTokenCount(ctx context.Context, conversationID uuid.UUID) (int, error)
}

// ListFilter represents filtering options for listing messages
type ListFilter struct {
	Limit    int    `json:"limit"`
	Offset   int    `json:"offset"`
	Role     *Role  `json:"role,omitempty"`
	SortBy   string `json:"sort_by,omitempty"` // created_at
	SortDesc bool   `json:"sort_desc,omitempty"`
}

// DefaultListFilter returns a default list filter
func DefaultListFilter() *ListFilter {
	return &ListFilter{
		Limit:    50,
		Offset:   0,
		SortBy:   "created_at",
		SortDesc: false, // Oldest first for conversation flow
	}
}
