package message

import (
	"time"

	"github.com/google/uuid"
)

// Message represents a chat message
type Message struct {
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ConversationID uuid.UUID `json:"conversation_id" gorm:"type:uuid;not null;index"`
	Role           Role      `json:"role" gorm:"type:varchar(20);not null"`
	Content        string    `json:"content" gorm:"type:text;not null"`
	TokenCount     int       `json:"token_count" gorm:"default:0"`
	Metadata       string    `json:"metadata" gorm:"type:jsonb"` // JSON metadata for additional info
	CreatedAt      time.Time `json:"created_at" gorm:"autoCreateTime"`
}

// Role represents the role of the message sender
type Role string

const (
	RoleUser      Role = "user"
	RoleAssistant Role = "assistant"
	RoleSystem    Role = "system"
)

// IsValid checks if the role is valid
func (r Role) IsValid() bool {
	switch r {
	case RoleUser, RoleAssistant, RoleSystem:
		return true
	default:
		return false
	}
}

// String returns the string representation of the role
func (r Role) String() string {
	return string(r)
}

// MessageRequest represents a request to send a message
type MessageRequest struct {
	Content string `json:"content" validate:"required,min=1,max=10000"`
}

// MessageResponse represents a message response
type MessageResponse struct {
	ID             uuid.UUID `json:"id"`
	ConversationID uuid.UUID `json:"conversation_id"`
	Role           Role      `json:"role"`
	Content        string    `json:"content"`
	TokenCount     int       `json:"token_count"`
	CreatedAt      time.Time `json:"created_at"`
}

// ChatRequest represents a complete chat request
type ChatRequest struct {
	ConversationID uuid.UUID `json:"conversation_id" validate:"required,uuid"`
	Message        string    `json:"message" validate:"required,min=1,max=10000"`
}

// ChatResponse represents a complete chat response
type ChatResponse struct {
	ConversationID uuid.UUID        `json:"conversation_id"`
	UserMessage    *MessageResponse `json:"user_message"`
	AssistantMessage *MessageResponse `json:"assistant_message"`
}

// Validate performs validation on the message entity
func (m *Message) Validate() error {
	if m.ConversationID == uuid.Nil {
		return ErrInvalidConversationID
	}
	if !m.Role.IsValid() {
		return ErrInvalidRole
	}
	if m.Content == "" {
		return ErrInvalidContent
	}
	return nil
}

// ToResponse converts Message to MessageResponse
func (m *Message) ToResponse() *MessageResponse {
	return &MessageResponse{
		ID:             m.ID,
		ConversationID: m.ConversationID,
		Role:           m.Role,
		Content:        m.Content,
		TokenCount:     m.TokenCount,
		CreatedAt:      m.CreatedAt,
	}
}

// IsUserMessage checks if the message is from a user
func (m *Message) IsUserMessage() bool {
	return m.Role == RoleUser
}

// IsAssistantMessage checks if the message is from an assistant
func (m *Message) IsAssistantMessage() bool {
	return m.Role == RoleAssistant
}

// IsSystemMessage checks if the message is a system message
func (m *Message) IsSystemMessage() bool {
	return m.Role == RoleSystem
}
