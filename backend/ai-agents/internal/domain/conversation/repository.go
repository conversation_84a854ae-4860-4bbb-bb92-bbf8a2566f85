package conversation

import (
	"context"

	"github.com/google/uuid"
)

// Repository defines the interface for conversation data operations
type Repository interface {
	// Create creates a new conversation
	Create(ctx context.Context, conversation *Conversation) error

	// GetByID retrieves a conversation by ID
	GetByID(ctx context.Context, id uuid.UUID) (*Conversation, error)

	// GetByUserID retrieves conversations for a specific user
	GetByUserID(ctx context.Context, userID uuid.UUID, filter *ListFilter) ([]*Conversation, error)

	// Update updates an existing conversation
	Update(ctx context.Context, conversation *Conversation) error

	// Delete soft deletes a conversation
	Delete(ctx context.Context, id uuid.UUID) error

	// HardDelete permanently deletes a conversation
	HardDelete(ctx context.Context, id uuid.UUID) error

	// Count returns the total count of conversations for a user
	Count(ctx context.Context, userID uuid.UUID, filter *ListFilter) (int64, error)
}

// ListFilter represents filtering options for listing conversations
type ListFilter struct {
	Limit    int        `json:"limit"`
	Offset   int        `json:"offset"`
	Status   *Status    `json:"status,omitempty"`
	AgentID  *uuid.UUID `json:"agent_id,omitempty"`
	Search   string     `json:"search,omitempty"` // Search in title
	SortBy   string     `json:"sort_by,omitempty"` // created_at, updated_at, title
	SortDesc bool       `json:"sort_desc,omitempty"`
}

// DefaultListFilter returns a default list filter
func DefaultListFilter() *ListFilter {
	return &ListFilter{
		Limit:    20,
		Offset:   0,
		SortBy:   "updated_at",
		SortDesc: true,
	}
}
