package conversation

import "errors"

// Conversation domain errors
var (
	ErrConversationNotFound = errors.New("conversation not found")
	ErrInvalidTitle         = errors.New("invalid conversation title")
	ErrInvalidUserID        = errors.New("invalid user ID")
	ErrInvalidAgentID       = errors.New("invalid agent ID")
	ErrInvalidStatus        = errors.New("invalid conversation status")
	ErrConversationDeleted  = errors.New("conversation is deleted")
	ErrUnauthorizedAccess   = errors.New("unauthorized access to conversation")
)
