package conversation

import (
	"time"

	"github.com/google/uuid"
)

// Conversation represents a chat conversation
type Conversation struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Title     string    `json:"title" gorm:"not null"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	AgentID   uuid.UUID `json:"agent_id" gorm:"type:uuid;not null;index"`
	Status    Status    `json:"status" gorm:"type:varchar(20);not null;default:'active'"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// Status represents conversation status
type Status string

const (
	StatusActive   Status = "active"
	StatusArchived Status = "archived"
	StatusDeleted  Status = "deleted"
)

// IsValid checks if the status is valid
func (s Status) IsValid() bool {
	switch s {
	case StatusActive, StatusArchived, StatusDeleted:
		return true
	default:
		return false
	}
}

// String returns the string representation of the status
func (s Status) String() string {
	return string(s)
}

// ConversationRequest represents a request to create a conversation
type ConversationRequest struct {
	Title   string    `json:"title" validate:"required,min=1,max=200"`
	AgentID uuid.UUID `json:"agent_id" validate:"required,uuid"`
}

// ConversationResponse represents a conversation response
type ConversationResponse struct {
	ID        uuid.UUID `json:"id"`
	Title     string    `json:"title"`
	UserID    uuid.UUID `json:"user_id"`
	AgentID   uuid.UUID `json:"agent_id"`
	Status    Status    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UpdateConversationRequest represents a request to update a conversation
type UpdateConversationRequest struct {
	Title  *string `json:"title,omitempty" validate:"omitempty,min=1,max=200"`
	Status *Status `json:"status,omitempty"`
}

// Validate performs validation on the conversation entity
func (c *Conversation) Validate() error {
	if c.Title == "" {
		return ErrInvalidTitle
	}
	if c.UserID == uuid.Nil {
		return ErrInvalidUserID
	}
	if c.AgentID == uuid.Nil {
		return ErrInvalidAgentID
	}
	if !c.Status.IsValid() {
		return ErrInvalidStatus
	}
	return nil
}

// ToResponse converts Conversation to ConversationResponse
func (c *Conversation) ToResponse() *ConversationResponse {
	return &ConversationResponse{
		ID:        c.ID,
		Title:     c.Title,
		UserID:    c.UserID,
		AgentID:   c.AgentID,
		Status:    c.Status,
		CreatedAt: c.CreatedAt,
		UpdatedAt: c.UpdatedAt,
	}
}

// UpdateFromRequest updates conversation fields from request
func (c *Conversation) UpdateFromRequest(req *UpdateConversationRequest) {
	if req.Title != nil {
		c.Title = *req.Title
	}
	if req.Status != nil {
		c.Status = *req.Status
	}
}

// IsOwnedBy checks if the conversation is owned by the specified user
func (c *Conversation) IsOwnedBy(userID uuid.UUID) bool {
	return c.UserID == userID
}

// IsActive checks if the conversation is active
func (c *Conversation) IsActive() bool {
	return c.Status == StatusActive
}
