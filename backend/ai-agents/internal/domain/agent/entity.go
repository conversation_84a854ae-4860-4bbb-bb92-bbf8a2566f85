package agent

import (
	"time"

	"github.com/google/uuid"
)

// Agent represents an AI agent configuration
type Agent struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"not null;index"`
	Description string    `json:"description" gorm:"type:text"`
	Provider    Provider  `json:"provider" gorm:"type:varchar(20);not null"`
	Model       string    `json:"model" gorm:"not null"`
	SystemPrompt string   `json:"system_prompt" gorm:"type:text"`
	Temperature float32   `json:"temperature" gorm:"default:0.7"`
	MaxTokens   int       `json:"max_tokens" gorm:"default:1000"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	IsPublic    bool      `json:"is_public" gorm:"default:false"`
	CreatedBy   uuid.UUID `json:"created_by" gorm:"type:uuid;not null;index"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// Provider represents AI service providers
type Provider string

const (
	ProviderOpenAI    Provider = "openai"
	ProviderAnthropic Provider = "anthropic"
	ProviderLocal     Provider = "local"
)

// IsValid checks if the provider is valid
func (p Provider) IsValid() bool {
	switch p {
	case ProviderOpenAI, ProviderAnthropic, ProviderLocal:
		return true
	default:
		return false
	}
}

// String returns the string representation of the provider
func (p Provider) String() string {
	return string(p)
}

// AgentRequest represents a request to create or update an agent
type AgentRequest struct {
	Name         string   `json:"name" validate:"required,min=1,max=100"`
	Description  string   `json:"description" validate:"max=500"`
	Provider     Provider `json:"provider" validate:"required"`
	Model        string   `json:"model" validate:"required,min=1,max=50"`
	SystemPrompt string   `json:"system_prompt" validate:"max=2000"`
	Temperature  float32  `json:"temperature" validate:"min=0,max=2"`
	MaxTokens    int      `json:"max_tokens" validate:"min=1,max=4000"`
	IsPublic     bool     `json:"is_public"`
}

// AgentResponse represents an agent response
type AgentResponse struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Provider    Provider  `json:"provider"`
	Model       string    `json:"model"`
	Temperature float32   `json:"temperature"`
	MaxTokens   int       `json:"max_tokens"`
	IsActive    bool      `json:"is_active"`
	IsPublic    bool      `json:"is_public"`
	CreatedBy   uuid.UUID `json:"created_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Validate performs validation on the agent entity
func (a *Agent) Validate() error {
	if a.Name == "" {
		return ErrInvalidName
	}
	if !a.Provider.IsValid() {
		return ErrInvalidProvider
	}
	if a.Model == "" {
		return ErrInvalidModel
	}
	if a.Temperature < 0 || a.Temperature > 2 {
		return ErrInvalidTemperature
	}
	if a.MaxTokens < 1 || a.MaxTokens > 4000 {
		return ErrInvalidMaxTokens
	}
	if a.CreatedBy == uuid.Nil {
		return ErrInvalidCreatedBy
	}
	return nil
}

// ToResponse converts Agent to AgentResponse
func (a *Agent) ToResponse() *AgentResponse {
	return &AgentResponse{
		ID:          a.ID,
		Name:        a.Name,
		Description: a.Description,
		Provider:    a.Provider,
		Model:       a.Model,
		Temperature: a.Temperature,
		MaxTokens:   a.MaxTokens,
		IsActive:    a.IsActive,
		IsPublic:    a.IsPublic,
		CreatedBy:   a.CreatedBy,
		CreatedAt:   a.CreatedAt,
		UpdatedAt:   a.UpdatedAt,
	}
}

// CanBeAccessedBy checks if the agent can be accessed by a user
func (a *Agent) CanBeAccessedBy(userID uuid.UUID) bool {
	return a.IsPublic || a.CreatedBy == userID
}

// UpdateFromRequest updates agent fields from request
func (a *Agent) UpdateFromRequest(req *AgentRequest) {
	a.Name = req.Name
	a.Description = req.Description
	a.Provider = req.Provider
	a.Model = req.Model
	a.SystemPrompt = req.SystemPrompt
	a.Temperature = req.Temperature
	a.MaxTokens = req.MaxTokens
	a.IsPublic = req.IsPublic
}
