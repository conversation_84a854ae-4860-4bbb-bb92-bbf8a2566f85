package agent

import (
	"context"

	"github.com/google/uuid"
)

// Repository defines the interface for agent data operations
type Repository interface {
	// Create creates a new agent
	Create(ctx context.Context, agent *Agent) error

	// GetByID retrieves an agent by ID
	GetByID(ctx context.Context, id uuid.UUID) (*Agent, error)

	// GetByName retrieves an agent by name and creator
	<PERSON><PERSON>y<PERSON>ame(ctx context.Context, name string, createdBy uuid.UUID) (*Agent, error)

	// List retrieves agents with pagination and filtering
	List(ctx context.Context, filter *ListFilter) ([]*Agent, error)

	// Update updates an existing agent
	Update(ctx context.Context, agent *Agent) error

	// Delete deletes an agent by ID
	Delete(ctx context.Context, id uuid.UUID) error

	// GetAccessibleAgents retrieves agents accessible by a user
	GetAccessibleAgents(ctx context.Context, userID uuid.UUID, filter *ListFilter) ([]*Agent, error)
}

// ListFilter represents filtering options for listing agents
type ListFilter struct {
	Limit     int       `json:"limit"`
	Offset    int       `json:"offset"`
	Provider  *Provider `json:"provider,omitempty"`
	IsActive  *bool     `json:"is_active,omitempty"`
	IsPublic  *bool     `json:"is_public,omitempty"`
	CreatedBy *uuid.UUID `json:"created_by,omitempty"`
	Search    string    `json:"search,omitempty"` // Search in name and description
}

// DefaultListFilter returns a default list filter
func DefaultListFilter() *ListFilter {
	return &ListFilter{
		Limit:  20,
		Offset: 0,
	}
}
