package agent

import "errors"

// Agent domain errors
var (
	ErrAgentNotFound      = errors.New("agent not found")
	ErrInvalidName        = errors.New("invalid agent name")
	ErrInvalidProvider    = errors.New("invalid provider")
	ErrInvalidModel       = errors.New("invalid model")
	ErrInvalidTemperature = errors.New("invalid temperature")
	ErrInvalidMaxTokens   = errors.New("invalid max tokens")
	ErrInvalidCreatedBy   = errors.New("invalid created by")
	ErrAgentNotAccessible = errors.New("agent not accessible")
	ErrDuplicateAgent     = errors.New("agent with this name already exists")
)
