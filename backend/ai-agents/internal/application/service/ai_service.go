package service

import (
	"context"

	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/go-auggie-trial/ai-agents/internal/domain/message"
)

// AIService defines the interface for AI operations
type AIService interface {
	// Chat sends a message to an AI agent and returns the response
	Chat(ctx context.Context, agent *agent.Agent, messages []*message.Message, userMessage string) (*ChatResponse, error)

	// ValidateAgent validates if an agent configuration is valid for the provider
	ValidateAgent(ctx context.Context, agent *agent.Agent) error

	// GetSupportedModels returns the list of supported models for a provider
	GetSupportedModels(ctx context.Context, provider agent.Provider) ([]string, error)
}

// ChatResponse represents the response from an AI service
type ChatResponse struct {
	Content    string            `json:"content"`
	TokenCount int               `json:"token_count"`
	Model      string            `json:"model"`
	Provider   agent.Provider    `json:"provider"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

// ChatMessage represents a message in the chat context
type ChatMessage struct {
	Role    message.Role `json:"role"`
	Content string       `json:"content"`
}

// ChatRequest represents a request to the AI service
type ChatRequest struct {
	Model       string        `json:"model"`
	Messages    []ChatMessage `json:"messages"`
	Temperature float32       `json:"temperature,omitempty"`
	MaxTokens   int           `json:"max_tokens,omitempty"`
	SystemPrompt string       `json:"system_prompt,omitempty"`
}

// AIProvider defines the interface that all AI providers must implement
type AIProvider interface {
	// Chat sends a chat request to the provider
	Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error)

	// ValidateModel checks if a model is supported by the provider
	ValidateModel(ctx context.Context, model string) error

	// GetSupportedModels returns the list of supported models
	GetSupportedModels(ctx context.Context) ([]string, error)

	// GetProvider returns the provider type
	GetProvider() agent.Provider
}

// AIServiceImpl implements the AIService interface
type AIServiceImpl struct {
	providers map[agent.Provider]AIProvider
}

// NewAIService creates a new AI service instance
func NewAIService(providers map[agent.Provider]AIProvider) AIService {
	return &AIServiceImpl{
		providers: providers,
	}
}

// Chat implements AIService.Chat
func (s *AIServiceImpl) Chat(ctx context.Context, agent *agent.Agent, messages []*message.Message, userMessage string) (*ChatResponse, error) {
	provider, exists := s.providers[agent.Provider]
	if !exists {
		return nil, ErrProviderNotSupported
	}

	// Build chat messages from conversation history
	chatMessages := make([]ChatMessage, 0, len(messages)+1)

	// Add system prompt if provided
	if agent.SystemPrompt != "" {
		chatMessages = append(chatMessages, ChatMessage{
			Role:    message.RoleSystem,
			Content: agent.SystemPrompt,
		})
	}

	// Add conversation history
	for _, msg := range messages {
		chatMessages = append(chatMessages, ChatMessage{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	// Add the new user message
	chatMessages = append(chatMessages, ChatMessage{
		Role:    message.RoleUser,
		Content: userMessage,
	})

	// Create the request
	request := &ChatRequest{
		Model:       agent.Model,
		Messages:    chatMessages,
		Temperature: agent.Temperature,
		MaxTokens:   agent.MaxTokens,
	}

	// Send to provider
	return provider.Chat(ctx, request)
}

// ValidateAgent implements AIService.ValidateAgent
func (s *AIServiceImpl) ValidateAgent(ctx context.Context, agent *agent.Agent) error {
	provider, exists := s.providers[agent.Provider]
	if !exists {
		return ErrProviderNotSupported
	}

	return provider.ValidateModel(ctx, agent.Model)
}

// GetSupportedModels implements AIService.GetSupportedModels
func (s *AIServiceImpl) GetSupportedModels(ctx context.Context, provider agent.Provider) ([]string, error) {
	aiProvider, exists := s.providers[provider]
	if !exists {
		return nil, ErrProviderNotSupported
	}

	return aiProvider.GetSupportedModels(ctx)
}
