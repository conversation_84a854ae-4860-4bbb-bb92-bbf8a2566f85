package service

import "errors"

// Service layer errors
var (
	ErrProviderNotSupported = errors.New("AI provider not supported")
	ErrModelNotSupported    = errors.New("model not supported by provider")
	ErrInvalidAPIKey        = errors.New("invalid API key")
	ErrRateLimitExceeded    = errors.New("rate limit exceeded")
	ErrQuotaExceeded        = errors.New("quota exceeded")
	ErrProviderUnavailable  = errors.New("AI provider unavailable")
	ErrInvalidRequest       = errors.New("invalid request to AI provider")
	ErrResponseTooLarge     = errors.New("AI response too large")
	ErrContextTooLong       = errors.New("conversation context too long")
)
