package usecase

import (
	"context"

	"github.com/go-auggie-trial/ai-agents/internal/application/service"
	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/google/uuid"
)

// AgentUseCase handles agent management business logic
type AgentUseCase struct {
	agentRepo agent.Repository
	aiService service.AIService
}

// NewAgentUseCase creates a new AgentUseCase
func NewAgentUseCase(agentRepo agent.Repository, aiService service.AIService) *AgentUseCase {
	return &AgentUseCase{
		agentRepo: agentRepo,
		aiService: aiService,
	}
}

// CreateAgent creates a new agent
func (uc *AgentUseCase) CreateAgent(ctx context.Context, userID uuid.UUID, req *agent.AgentRequest) (*agent.Agent, error) {
	// Check if agent with same name already exists for this user
	existingAgent, err := uc.agentRepo.GetByName(ctx, req.Name, userID)
	if err == nil && existingAgent != nil {
		return nil, agent.ErrDuplicateAgent
	}
	if err != nil && err != agent.ErrAgentNotFound {
		return nil, err
	}

	// Create agent entity
	agentEntity := &agent.Agent{
		ID:           uuid.New(),
		Name:         req.Name,
		Description:  req.Description,
		Provider:     req.Provider,
		Model:        req.Model,
		SystemPrompt: req.SystemPrompt,
		Temperature:  req.Temperature,
		MaxTokens:    req.MaxTokens,
		IsActive:     true,
		IsPublic:     req.IsPublic,
		CreatedBy:    userID,
	}

	// Validate agent
	if err := agentEntity.Validate(); err != nil {
		return nil, err
	}

	// Validate with AI service
	if err := uc.aiService.ValidateAgent(ctx, agentEntity); err != nil {
		return nil, err
	}

	// Save agent
	if err := uc.agentRepo.Create(ctx, agentEntity); err != nil {
		return nil, err
	}

	return agentEntity, nil
}

// GetAgent retrieves an agent by ID
func (uc *AgentUseCase) GetAgent(ctx context.Context, userID, agentID uuid.UUID) (*agent.Agent, error) {
	agentEntity, err := uc.agentRepo.GetByID(ctx, agentID)
	if err != nil {
		return nil, err
	}

	// Check access permissions
	if !agentEntity.CanBeAccessedBy(userID) {
		return nil, agent.ErrAgentNotAccessible
	}

	return agentEntity, nil
}

// ListAgents retrieves agents accessible by a user
func (uc *AgentUseCase) ListAgents(ctx context.Context, userID uuid.UUID, filter *agent.ListFilter) ([]*agent.Agent, error) {
	if filter == nil {
		filter = agent.DefaultListFilter()
	}

	return uc.agentRepo.GetAccessibleAgents(ctx, userID, filter)
}

// ListMyAgents retrieves agents created by a user
func (uc *AgentUseCase) ListMyAgents(ctx context.Context, userID uuid.UUID, filter *agent.ListFilter) ([]*agent.Agent, error) {
	if filter == nil {
		filter = agent.DefaultListFilter()
	}

	// Set filter to only show user's agents
	filter.CreatedBy = &userID

	return uc.agentRepo.List(ctx, filter)
}

// UpdateAgent updates an existing agent
func (uc *AgentUseCase) UpdateAgent(ctx context.Context, userID, agentID uuid.UUID, req *agent.AgentRequest) (*agent.Agent, error) {
	// Get existing agent
	agentEntity, err := uc.agentRepo.GetByID(ctx, agentID)
	if err != nil {
		return nil, err
	}

	// Check ownership
	if agentEntity.CreatedBy != userID {
		return nil, agent.ErrAgentNotAccessible
	}

	// Check for name conflicts (excluding current agent)
	if req.Name != agentEntity.Name {
		existingAgent, err := uc.agentRepo.GetByName(ctx, req.Name, userID)
		if err == nil && existingAgent != nil && existingAgent.ID != agentID {
			return nil, agent.ErrDuplicateAgent
		}
		if err != nil && err != agent.ErrAgentNotFound {
			return nil, err
		}
	}

	// Update fields
	agentEntity.UpdateFromRequest(req)

	// Validate updated agent
	if err := agentEntity.Validate(); err != nil {
		return nil, err
	}

	// Validate with AI service
	if err := uc.aiService.ValidateAgent(ctx, agentEntity); err != nil {
		return nil, err
	}

	// Save changes
	if err := uc.agentRepo.Update(ctx, agentEntity); err != nil {
		return nil, err
	}

	return agentEntity, nil
}

// DeleteAgent deletes an agent
func (uc *AgentUseCase) DeleteAgent(ctx context.Context, userID, agentID uuid.UUID) error {
	// Get existing agent
	agentEntity, err := uc.agentRepo.GetByID(ctx, agentID)
	if err != nil {
		return err
	}

	// Check ownership
	if agentEntity.CreatedBy != userID {
		return agent.ErrAgentNotAccessible
	}

	// Delete agent
	return uc.agentRepo.Delete(ctx, agentID)
}

// ToggleAgentStatus toggles the active status of an agent
func (uc *AgentUseCase) ToggleAgentStatus(ctx context.Context, userID, agentID uuid.UUID) (*agent.Agent, error) {
	// Get existing agent
	agentEntity, err := uc.agentRepo.GetByID(ctx, agentID)
	if err != nil {
		return nil, err
	}

	// Check ownership
	if agentEntity.CreatedBy != userID {
		return nil, agent.ErrAgentNotAccessible
	}

	// Toggle status
	agentEntity.IsActive = !agentEntity.IsActive

	// Save changes
	if err := uc.agentRepo.Update(ctx, agentEntity); err != nil {
		return nil, err
	}

	return agentEntity, nil
}

// GetSupportedModels returns supported models for a provider
func (uc *AgentUseCase) GetSupportedModels(ctx context.Context, provider agent.Provider) ([]string, error) {
	return uc.aiService.GetSupportedModels(ctx, provider)
}
