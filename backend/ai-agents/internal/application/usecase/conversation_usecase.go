package usecase

import (
	"context"

	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/go-auggie-trial/ai-agents/internal/domain/conversation"
	"github.com/google/uuid"
)

// ConversationUseCase handles conversation management business logic
type ConversationUseCase struct {
	conversationRepo conversation.Repository
	agentRepo        agent.Repository
}

// NewConversationUseCase creates a new ConversationUseCase
func NewConversationUseCase(
	conversationRepo conversation.Repository,
	agentRepo agent.Repository,
) *ConversationUseCase {
	return &ConversationUseCase{
		conversationRepo: conversationRepo,
		agentRepo:        agentRepo,
	}
}

// CreateConversation creates a new conversation
func (uc *ConversationUseCase) CreateConversation(ctx context.Context, userID uuid.UUID, req *conversation.ConversationRequest) (*conversation.Conversation, error) {
	// Validate that the agent exists and is accessible
	agentEntity, err := uc.agentRepo.GetByID(ctx, req.AgentID)
	if err != nil {
		return nil, err
	}

	if !agentEntity.CanBeAccessedBy(userID) {
		return nil, agent.ErrAgentNotAccessible
	}

	// Create conversation
	conv := &conversation.Conversation{
		ID:      uuid.New(),
		Title:   req.Title,
		UserID:  userID,
		AgentID: req.AgentID,
		Status:  conversation.StatusActive,
	}

	// Validate conversation
	if err := conv.Validate(); err != nil {
		return nil, err
	}

	// Save conversation
	if err := uc.conversationRepo.Create(ctx, conv); err != nil {
		return nil, err
	}

	return conv, nil
}

// GetConversation retrieves a conversation by ID
func (uc *ConversationUseCase) GetConversation(ctx context.Context, userID, conversationID uuid.UUID) (*conversation.Conversation, error) {
	conv, err := uc.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return nil, err
	}

	// Check ownership
	if !conv.IsOwnedBy(userID) {
		return nil, conversation.ErrUnauthorizedAccess
	}

	return conv, nil
}

// ListConversations retrieves conversations for a user
func (uc *ConversationUseCase) ListConversations(ctx context.Context, userID uuid.UUID, filter *conversation.ListFilter) ([]*conversation.Conversation, error) {
	if filter == nil {
		filter = conversation.DefaultListFilter()
	}

	return uc.conversationRepo.GetByUserID(ctx, userID, filter)
}

// UpdateConversation updates an existing conversation
func (uc *ConversationUseCase) UpdateConversation(ctx context.Context, userID, conversationID uuid.UUID, req *conversation.UpdateConversationRequest) (*conversation.Conversation, error) {
	// Get existing conversation
	conv, err := uc.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return nil, err
	}

	// Check ownership
	if !conv.IsOwnedBy(userID) {
		return nil, conversation.ErrUnauthorizedAccess
	}

	// Update fields
	conv.UpdateFromRequest(req)

	// Validate updated conversation
	if err := conv.Validate(); err != nil {
		return nil, err
	}

	// Save changes
	if err := uc.conversationRepo.Update(ctx, conv); err != nil {
		return nil, err
	}

	return conv, nil
}

// DeleteConversation soft deletes a conversation
func (uc *ConversationUseCase) DeleteConversation(ctx context.Context, userID, conversationID uuid.UUID) error {
	// Get existing conversation
	conv, err := uc.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return err
	}

	// Check ownership
	if !conv.IsOwnedBy(userID) {
		return conversation.ErrUnauthorizedAccess
	}

	// Soft delete
	return uc.conversationRepo.Delete(ctx, conversationID)
}

// ArchiveConversation archives a conversation
func (uc *ConversationUseCase) ArchiveConversation(ctx context.Context, userID, conversationID uuid.UUID) (*conversation.Conversation, error) {
	// Get existing conversation
	conv, err := uc.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return nil, err
	}

	// Check ownership
	if !conv.IsOwnedBy(userID) {
		return nil, conversation.ErrUnauthorizedAccess
	}

	// Update status
	conv.Status = conversation.StatusArchived

	// Save changes
	if err := uc.conversationRepo.Update(ctx, conv); err != nil {
		return nil, err
	}

	return conv, nil
}

// RestoreConversation restores an archived conversation
func (uc *ConversationUseCase) RestoreConversation(ctx context.Context, userID, conversationID uuid.UUID) (*conversation.Conversation, error) {
	// Get existing conversation
	conv, err := uc.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return nil, err
	}

	// Check ownership
	if !conv.IsOwnedBy(userID) {
		return nil, conversation.ErrUnauthorizedAccess
	}

	// Update status
	conv.Status = conversation.StatusActive

	// Save changes
	if err := uc.conversationRepo.Update(ctx, conv); err != nil {
		return nil, err
	}

	return conv, nil
}

// GetConversationCount returns the total count of conversations for a user
func (uc *ConversationUseCase) GetConversationCount(ctx context.Context, userID uuid.UUID, filter *conversation.ListFilter) (int64, error) {
	if filter == nil {
		filter = conversation.DefaultListFilter()
	}

	return uc.conversationRepo.Count(ctx, userID, filter)
}
