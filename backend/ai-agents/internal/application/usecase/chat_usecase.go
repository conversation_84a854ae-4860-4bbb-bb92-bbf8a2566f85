package usecase

import (
	"context"
	"fmt"

	"github.com/go-auggie-trial/ai-agents/internal/application/service"
	"github.com/go-auggie-trial/ai-agents/internal/domain/agent"
	"github.com/go-auggie-trial/ai-agents/internal/domain/conversation"
	"github.com/go-auggie-trial/ai-agents/internal/domain/message"
	"github.com/google/uuid"
)

// ChatUseCase handles chat operations business logic
type ChatUseCase struct {
	conversationRepo conversation.Repository
	messageRepo      message.Repository
	agentRepo        agent.Repository
	aiService        service.AIService
}

// NewChatUseCase creates a new ChatUseCase
func NewChatUseCase(
	conversationRepo conversation.Repository,
	messageRepo message.Repository,
	agentRepo agent.Repository,
	aiService service.AIService,
) *ChatUseCase {
	return &ChatUseCase{
		conversationRepo: conversationRepo,
		messageRepo:      messageRepo,
		agentRepo:        agentRepo,
		aiService:        aiService,
	}
}

// SendMessage sends a message in a conversation and gets AI response
func (uc *ChatUseCase) SendMessage(ctx context.Context, userID uuid.UUID, req *message.ChatRequest) (*message.ChatResponse, error) {
	// Get and validate conversation
	conv, err := uc.conversationRepo.GetByID(ctx, req.ConversationID)
	if err != nil {
		return nil, err
	}

	if !conv.IsOwnedBy(userID) {
		return nil, conversation.ErrUnauthorizedAccess
	}

	if !conv.IsActive() {
		return nil, conversation.ErrConversationDeleted
	}

	// Get agent
	agentEntity, err := uc.agentRepo.GetByID(ctx, conv.AgentID)
	if err != nil {
		return nil, err
	}

	if !agentEntity.IsActive {
		return nil, agent.ErrAgentNotAccessible
	}

	// Get conversation history (last 20 messages for context)
	history, err := uc.messageRepo.GetConversationHistory(ctx, req.ConversationID, 20)
	if err != nil {
		return nil, err
	}

	// Create user message
	userMessage := &message.Message{
		ID:             uuid.New(),
		ConversationID: req.ConversationID,
		Role:           message.RoleUser,
		Content:        req.Message,
		TokenCount:     0, // Will be updated after AI response
	}

	if err := userMessage.Validate(); err != nil {
		return nil, err
	}

	// Save user message
	if err := uc.messageRepo.Create(ctx, userMessage); err != nil {
		return nil, err
	}

	// Get AI response
	aiResponse, err := uc.aiService.Chat(ctx, agentEntity, history, req.Message)
	if err != nil {
		return nil, fmt.Errorf("failed to get AI response: %w", err)
	}

	// Create assistant message
	assistantMessage := &message.Message{
		ID:             uuid.New(),
		ConversationID: req.ConversationID,
		Role:           message.RoleAssistant,
		Content:        aiResponse.Content,
		TokenCount:     aiResponse.TokenCount,
	}

	if err := assistantMessage.Validate(); err != nil {
		return nil, err
	}

	// Save assistant message
	if err := uc.messageRepo.Create(ctx, assistantMessage); err != nil {
		return nil, err
	}

	// Update conversation timestamp
	if err := uc.conversationRepo.Update(ctx, conv); err != nil {
		// Log error but don't fail the request
		// TODO: Add proper logging
	}

	// Build response
	return &message.ChatResponse{
		ConversationID:   req.ConversationID,
		UserMessage:      userMessage.ToResponse(),
		AssistantMessage: assistantMessage.ToResponse(),
	}, nil
}

// GetMessages retrieves messages for a conversation
func (uc *ChatUseCase) GetMessages(ctx context.Context, userID, conversationID uuid.UUID, filter *message.ListFilter) ([]*message.Message, error) {
	// Validate conversation access
	conv, err := uc.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return nil, err
	}

	if !conv.IsOwnedBy(userID) {
		return nil, conversation.ErrUnauthorizedAccess
	}

	if filter == nil {
		filter = message.DefaultListFilter()
	}

	return uc.messageRepo.GetByConversationID(ctx, conversationID, filter)
}

// GetMessage retrieves a specific message
func (uc *ChatUseCase) GetMessage(ctx context.Context, userID, messageID uuid.UUID) (*message.Message, error) {
	// Get message
	msg, err := uc.messageRepo.GetByID(ctx, messageID)
	if err != nil {
		return nil, err
	}

	// Validate conversation access
	conv, err := uc.conversationRepo.GetByID(ctx, msg.ConversationID)
	if err != nil {
		return nil, err
	}

	if !conv.IsOwnedBy(userID) {
		return nil, conversation.ErrUnauthorizedAccess
	}

	return msg, nil
}

// DeleteMessage deletes a message
func (uc *ChatUseCase) DeleteMessage(ctx context.Context, userID, messageID uuid.UUID) error {
	// Get message
	msg, err := uc.messageRepo.GetByID(ctx, messageID)
	if err != nil {
		return err
	}

	// Validate conversation access
	conv, err := uc.conversationRepo.GetByID(ctx, msg.ConversationID)
	if err != nil {
		return err
	}

	if !conv.IsOwnedBy(userID) {
		return conversation.ErrUnauthorizedAccess
	}

	// Delete message
	return uc.messageRepo.Delete(ctx, messageID)
}

// GetConversationTokenCount returns the total token count for a conversation
func (uc *ChatUseCase) GetConversationTokenCount(ctx context.Context, userID, conversationID uuid.UUID) (int, error) {
	// Validate conversation access
	conv, err := uc.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return 0, err
	}

	if !conv.IsOwnedBy(userID) {
		return 0, conversation.ErrUnauthorizedAccess
	}

	return uc.messageRepo.GetTokenCount(ctx, conversationID)
}

// ClearConversation deletes all messages in a conversation
func (uc *ChatUseCase) ClearConversation(ctx context.Context, userID, conversationID uuid.UUID) error {
	// Validate conversation access
	conv, err := uc.conversationRepo.GetByID(ctx, conversationID)
	if err != nil {
		return err
	}

	if !conv.IsOwnedBy(userID) {
		return conversation.ErrUnauthorizedAccess
	}

	// Delete all messages
	return uc.messageRepo.DeleteByConversationID(ctx, conversationID)
}
