package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/go-auggie-trial/ai-agents/internal/infrastructure/config"
	"github.com/go-auggie-trial/ai-agents/internal/infrastructure/database"
	"github.com/go-auggie-trial/ai-agents/internal/infrastructure/http/server"
	"github.com/go-auggie-trial/ai-agents/pkg/logger"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	appLogger := logger.New()
	appLogger.Info("Configuration loaded successfully")

	// Initialize database
	db, err := database.NewConnectionWithConfig(&cfg.Database)
	if err != nil {
		appLogger.Fatal("Failed to connect to database", "error", err)
	}

	// Run database migrations
	if err := database.RunMigrations(db); err != nil {
		appLogger.Fatal("Failed to run database migrations", "error", err)
	}

	// Initialize HTTP server
	httpServer := server.New(db, appLogger)

	// Start server in a goroutine
	go func() {
		appLogger.Info("Starting AI Agents server", "port", cfg.Server.Port)
		if err := httpServer.Start(":" + cfg.Server.Port); err != nil && err != http.ErrServerClosed {
			appLogger.Fatal("Failed to start server", "error", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	appLogger.Info("Shutting down server...")

	// Create a context with timeout for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown the server
	if err := httpServer.Shutdown(ctx); err != nil {
		appLogger.Fatal("Server forced to shutdown", "error", err)
	}

	appLogger.Info("Server exited")
}
