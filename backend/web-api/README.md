# Web API Backend

A Go backend web server built with Echo framework following clean architecture and domain-driven design principles. This API provides authentication, authorization, user management, and chat functionality.

## Features

- **Authentication & Authorization**: JWT-based authentication with refresh tokens
- **User Management**: User registration, login, profile management
- **Chat System**: Send, receive, update, and delete chat messages
- **Clean Architecture**: Domain-driven design with clear separation of concerns
- **Database**: PostgreSQL with GORM ORM
- **Validation**: Request validation using go-playground/validator
- **Middleware**: CORS, logging, recovery, timeout, and authentication middleware
- **Docker Support**: Full containerization with Docker Compose

## Architecture

The project follows clean architecture principles with the following layers:

```
├── cmd/server/          # Application entry point
├── internal/
│   ├── domain/          # Domain entities, value objects, and repository interfaces
│   │   ├── auth/        # Authentication domain
│   │   ├── chat/        # Chat domain
│   │   └── user/        # User domain
│   ├── application/     # Use cases and business logic
│   │   └── usecase/     # Application use cases
│   ├── infrastructure/  # External concerns (database, JWT, etc.)
│   │   ├── config/      # Configuration management
│   │   ├── database/    # Database repositories and connection
│   │   ├── hash/        # Password hashing service
│   │   └── jwt/         # JWT token service
│   └── interface/       # HTTP handlers, middleware, and DTOs
│       ├── dto/         # Data Transfer Objects
│       └── http/        # HTTP layer (handlers, middleware, router)
└── pkg/                 # Shared packages
    ├── errors/          # Custom error types
    ├── logger/          # Logging utilities
    └── validator/       # Request validation
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout

### Chat (Protected)
- `POST /api/v1/chat/messages` - Send a message
- `GET /api/v1/chat/messages` - Get user messages (paginated)
- `GET /api/v1/chat/messages/:id` - Get specific message
- `PUT /api/v1/chat/messages/:id` - Update message
- `DELETE /api/v1/chat/messages/:id` - Delete message

### Health Check
- `GET /health` - Service health check

## Getting Started

### Prerequisites

- Go 1.21 or higher
- Docker and Docker Compose
- PostgreSQL (if running locally)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend/web-api
   ```

2. **Copy environment variables**
   ```bash
   cp .env.example .env
   ```

3. **Update environment variables**
   Edit `.env` file with your configuration, especially:
   - Database credentials
   - JWT secret key
   - Server configuration

4. **Run with Docker Compose (Recommended)**
   ```bash
   docker-compose up -d
   ```

   This will start:
   - PostgreSQL database on port 5432
   - Redis on port 6379
   - Web API on port 8080
   - pgAdmin on port 5050

5. **Or run locally**
   ```bash
   # Install dependencies
   go mod download
   
   # Run database migrations (ensure PostgreSQL is running)
   go run cmd/server/main.go
   ```

### Testing the API

1. **Health Check**
   ```bash
   curl http://localhost:8080/health
   ```

2. **Register a user**
   ```bash
   curl -X POST http://localhost:8080/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "username": "testuser",
       "password": "password123",
       "first_name": "Test",
       "last_name": "User"
     }'
   ```

3. **Login**
   ```bash
   curl -X POST http://localhost:8080/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "password123"
     }'
   ```

4. **Send a chat message** (use the access token from login)
   ```bash
   curl -X POST http://localhost:8080/api/v1/chat/messages \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -d '{
       "content": "Hello, world!",
       "type": "text"
     }'
   ```

## Configuration

The application uses environment variables for configuration. See `.env.example` for all available options.

Key configuration options:
- `JWT_SECRET_KEY`: Secret key for JWT token signing (change in production!)
- `DB_PASSWORD`: Database password
- `SERVER_PORT`: Server port (default: 8080)

## Database

The application uses PostgreSQL with automatic migrations. The database schema includes:
- `users` - User accounts
- `refresh_tokens` - JWT refresh tokens
- `messages` - Chat messages

## Security Features

- Password hashing using bcrypt
- JWT access tokens (short-lived)
- JWT refresh tokens (long-lived, revocable)
- Request validation
- CORS protection
- Rate limiting ready (can be added)

## Development

### Adding New Features

1. **Domain Layer**: Add entities and repository interfaces in `internal/domain/`
2. **Application Layer**: Add use cases in `internal/application/usecase/`
3. **Infrastructure Layer**: Add implementations in `internal/infrastructure/`
4. **Interface Layer**: Add HTTP handlers and DTOs in `internal/interface/`

### Running Tests

```bash
go test ./...
```

## Production Deployment

1. **Build the Docker image**
   ```bash
   docker build -t web-api .
   ```

2. **Set production environment variables**
   - Use strong JWT secret key
   - Configure proper database credentials
   - Set appropriate log levels

3. **Deploy with proper security**
   - Use HTTPS
   - Configure firewall rules
   - Set up monitoring and logging
   - Use environment-specific configurations

## Contributing

1. Follow Go best practices
2. Maintain clean architecture principles
3. Add tests for new features
4. Update documentation

## License

[Add your license here]
