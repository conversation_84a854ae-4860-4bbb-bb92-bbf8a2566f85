version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: web-api-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: web_api
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - web-api-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d web_api"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis (for caching, sessions, etc.)
  redis:
    image: redis:7-alpine
    container_name: web-api-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - web-api-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Web API Application
  web-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web-api-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Server Configuration
      SERVER_HOST: 0.0.0.0
      SERVER_PORT: 8080
      SERVER_READ_TIMEOUT: 10s
      SERVER_WRITE_TIMEOUT: 10s
      SERVER_IDLE_TIMEOUT: 60s
      
      # Database Configuration
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres123
      DB_NAME: web_api
      DB_SSL_MODE: disable
      
      # JWT Configuration
      JWT_SECRET_KEY: your-super-secret-jwt-key-change-this-in-production
      JWT_ACCESS_TOKEN_DURATION: 15m
      JWT_REFRESH_TOKEN_DURATION: 168h
      
      # Logging Configuration
      LOG_LEVEL: info
      LOG_FORMAT: json
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - web-api-network
    volumes:
      - ./logs:/app/logs

  # pgAdmin (Database Management Tool)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: web-api-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - web-api-network
    depends_on:
      - postgres

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  web-api-network:
    driver: bridge
