# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=web-api
BINARY_UNIX=$(BINARY_NAME)_unix

# Docker parameters
DOCKER_IMAGE=web-api
DOCKER_TAG=latest

.PHONY: all build clean test coverage deps run docker-build docker-run docker-stop help

all: test build

## Build the binary
build:
	$(GOBUILD) -o $(BINARY_NAME) -v ./cmd/server

## Clean build files
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)

## Run tests
test:
	$(GOTEST) -v ./...

## Run tests with coverage
coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

## Download dependencies
deps:
	$(GOMOD) download
	$(GOMOD) tidy

## Run the application
run:
	$(GOCMD) run ./cmd/server

## Build for Linux
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v ./cmd/server

## Docker build
docker-build:
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

## Docker run with compose
docker-up:
	docker-compose up -d

## Docker stop
docker-down:
	docker-compose down

## Docker logs
docker-logs:
	docker-compose logs -f web-api

## Database migration (when running locally)
migrate:
	$(GOCMD) run ./cmd/server

## Format code
fmt:
	$(GOCMD) fmt ./...

## Lint code (requires golangci-lint)
lint:
	golangci-lint run

## Security check (requires gosec)
security:
	gosec ./...

## Generate API documentation (requires swag)
docs:
	swag init -g cmd/server/main.go

## Install development tools
install-tools:
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint
	$(GOGET) -u github.com/securecodewarrior/gosec/v2/cmd/gosec
	$(GOGET) -u github.com/swaggo/swag/cmd/swag

## Show help
help:
	@echo ''
	@echo 'Usage:'
	@echo '  make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} { \
		if (/^[a-zA-Z_-]+:.*?##.*$$/) printf "  %-20s%s\n", $$1, $$2 \
	}' $(MAKEFILE_LIST)
