package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/labstack/echo/v4"

	"github.com/go-auggie-trial/web-api/internal/application/usecase"
	"github.com/go-auggie-trial/web-api/internal/infrastructure/config"
	"github.com/go-auggie-trial/web-api/internal/infrastructure/database"
	"github.com/go-auggie-trial/web-api/internal/infrastructure/hash"
	"github.com/go-auggie-trial/web-api/internal/infrastructure/jwt"
	"github.com/go-auggie-trial/web-api/internal/interface/http/handler"
	"github.com/go-auggie-trial/web-api/internal/interface/http/middleware"
	"github.com/go-auggie-trial/web-api/internal/interface/http/router"
	"github.com/go-auggie-trial/web-api/pkg/validator"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database connection
	dbConfig := &database.Config{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		DBName:   cfg.Database.DBName,
		SSLMode:  cfg.Database.SSLMode,
	}

	db, err := database.NewConnection(dbConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Run database migrations
	if err := database.Migrate(db); err != nil {
		log.Fatalf("Failed to run database migrations: %v", err)
	}

	// Initialize repositories
	userRepo := database.NewUserRepository(db)
	refreshTokenRepo := database.NewRefreshTokenRepository(db)
	chatRepo := database.NewChatRepository(db)

	// Initialize services
	passwordService := hash.NewPasswordService()
	
	jwtConfig := &jwt.Config{
		SecretKey:            cfg.JWT.SecretKey,
		AccessTokenDuration:  cfg.JWT.AccessTokenDuration,
		RefreshTokenDuration: cfg.JWT.RefreshTokenDuration,
	}
	tokenService := jwt.NewTokenService(jwtConfig, refreshTokenRepo)

	// Initialize use cases
	authUseCase := usecase.NewAuthUseCase(userRepo, refreshTokenRepo, tokenService, passwordService)
	chatUseCase := usecase.NewChatUseCase(chatRepo)

	// Initialize validator
	v := validator.New()

	// Initialize handlers
	authHandler := handler.NewAuthHandler(authUseCase, v)
	chatHandler := handler.NewChatHandler(chatUseCase, v)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(authUseCase)

	// Initialize router
	r := router.NewRouter(authHandler, chatHandler, authMiddleware)

	// Initialize Echo server
	e := echo.New()
	e.HideBanner = true

	// Setup routes
	r.SetupRoutes(e)

	// Start server in a goroutine
	go func() {
		address := cfg.GetServerAddress()
		log.Printf("Starting server on %s", address)
		
		server := &http.Server{
			Addr:         address,
			ReadTimeout:  cfg.Server.ReadTimeout,
			WriteTimeout: cfg.Server.WriteTimeout,
			IdleTimeout:  cfg.Server.IdleTimeout,
		}

		if err := e.StartServer(server); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Create a context with timeout for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Shutdown the server
	if err := e.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	// Close database connection
	if err := database.Close(db); err != nil {
		log.Printf("Error closing database connection: %v", err)
	}

	log.Println("Server exited")
}
