package hash

import (
	"golang.org/x/crypto/bcrypt"

	"github.com/go-auggie-trial/web-api/internal/domain/auth"
)

// PasswordService implements auth.PasswordService interface
type PasswordService struct {
	cost int
}

// NewPasswordService creates a new PasswordService
func NewPasswordService() auth.PasswordService {
	return &PasswordService{
		cost: bcrypt.DefaultCost,
	}
}

// NewPasswordServiceWithCost creates a new PasswordService with custom cost
func NewPasswordServiceWithCost(cost int) auth.PasswordService {
	return &PasswordService{
		cost: cost,
	}
}

// HashPassword hashes a plain text password
func (s *PasswordService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), s.cost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// ComparePassword compares a plain text password with a hashed password
func (s *PasswordService) ComparePassword(hashedPassword, password string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	if err != nil {
		if err == bcrypt.ErrMismatchedHashAndPassword {
			return auth.ErrInvalidCredentials
		}
		return err
	}
	return nil
}
