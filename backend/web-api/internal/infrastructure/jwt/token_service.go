package jwt

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"

	"github.com/go-auggie-trial/web-api/internal/domain/auth"
)

// TokenService implements auth.TokenService interface
type TokenService struct {
	secretKey            []byte
	accessTokenDuration  time.Duration
	refreshTokenDuration time.Duration
	refreshTokenRepo     auth.RefreshTokenRepository
}

// Config holds JWT configuration
type Config struct {
	SecretKey            string
	AccessTokenDuration  time.Duration
	RefreshTokenDuration time.Duration
}

// NewTokenService creates a new TokenService
func NewTokenService(config *Config, refreshTokenRepo auth.RefreshTokenRepository) auth.TokenService {
	return &TokenService{
		secretKey:            []byte(config.SecretKey),
		accessTokenDuration:  config.AccessTokenDuration,
		refreshTokenDuration: config.RefreshTokenDuration,
		refreshTokenRepo:     refreshTokenRepo,
	}
}

// GenerateTokenPair generates access and refresh tokens for a user
func (s *TokenService) GenerateTokenPair(ctx context.Context, userID uuid.UUID, email, username, role string) (*auth.TokenPair, error) {
	now := time.Now()
	accessTokenExpiry := now.Add(s.accessTokenDuration)
	refreshTokenExpiry := now.Add(s.refreshTokenDuration)

	// Generate access token
	accessToken, err := s.generateAccessToken(userID, email, username, role, now, accessTokenExpiry)
	if err != nil {
		return nil, fmt.Errorf("failed to generate access token: %w", err)
	}

	// Generate refresh token
	refreshTokenString, err := s.generateRandomToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	// Store refresh token in database
	refreshToken := &auth.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID,
		Token:     refreshTokenString,
		ExpiresAt: refreshTokenExpiry,
	}

	if err := s.refreshTokenRepo.Create(ctx, refreshToken); err != nil {
		return nil, fmt.Errorf("failed to store refresh token: %w", err)
	}

	return &auth.TokenPair{
		AccessToken:  accessToken,
		RefreshToken: refreshTokenString,
		ExpiresAt:    accessTokenExpiry,
	}, nil
}

// ValidateAccessToken validates an access token and returns claims
func (s *TokenService) ValidateAccessToken(ctx context.Context, tokenString string) (*auth.Claims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.secretKey, nil
	})

	if err != nil {
		return nil, auth.ErrTokenInvalid
	}

	if !token.Valid {
		return nil, auth.ErrTokenInvalid
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, auth.ErrTokenInvalid
	}

	// Extract claims
	userIDStr, ok := claims["user_id"].(string)
	if !ok {
		return nil, auth.ErrTokenInvalid
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, auth.ErrTokenInvalid
	}

	email, ok := claims["email"].(string)
	if !ok {
		return nil, auth.ErrTokenInvalid
	}

	username, ok := claims["username"].(string)
	if !ok {
		return nil, auth.ErrTokenInvalid
	}

	role, ok := claims["role"].(string)
	if !ok {
		return nil, auth.ErrTokenInvalid
	}

	iat, ok := claims["iat"].(float64)
	if !ok {
		return nil, auth.ErrTokenInvalid
	}

	exp, ok := claims["exp"].(float64)
	if !ok {
		return nil, auth.ErrTokenInvalid
	}

	return &auth.Claims{
		UserID:    userID,
		Email:     email,
		Username:  username,
		Role:      role,
		IssuedAt:  int64(iat),
		ExpiresAt: int64(exp),
	}, nil
}

// RefreshAccessToken generates a new access token using refresh token
func (s *TokenService) RefreshAccessToken(ctx context.Context, refreshTokenString string) (*auth.TokenPair, error) {
	// Get refresh token from database
	refreshToken, err := s.refreshTokenRepo.GetByToken(ctx, refreshTokenString)
	if err != nil {
		return nil, auth.ErrTokenNotFound
	}

	// Check if token is valid
	if !refreshToken.IsValid() {
		return nil, auth.ErrTokenExpired
	}

	// Revoke the old refresh token
	refreshToken.Revoke()
	if err := s.refreshTokenRepo.Update(ctx, refreshToken); err != nil {
		return nil, fmt.Errorf("failed to revoke old refresh token: %w", err)
	}

	// Note: In a complete implementation, you would fetch user details here
	// For now, we'll generate a new token pair with minimal user info
	// This requires the user repository to be injected into the token service

	// Generate new refresh token
	now := time.Now()
	accessTokenExpiry := now.Add(s.accessTokenDuration)
	refreshTokenExpiry := now.Add(s.refreshTokenDuration)

	// Create new refresh token
	newRefreshTokenString, err := s.generateRandomToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate new refresh token: %w", err)
	}

	newRefreshToken := &auth.RefreshToken{
		ID:        uuid.New(),
		UserID:    refreshToken.UserID,
		Token:     newRefreshTokenString,
		ExpiresAt: refreshTokenExpiry,
	}

	if err := s.refreshTokenRepo.Create(ctx, newRefreshToken); err != nil {
		return nil, fmt.Errorf("failed to store new refresh token: %w", err)
	}

	// Generate new access token (with placeholder user info)
	// In production, fetch actual user details from database
	accessToken, err := s.generateAccessToken(
		refreshToken.UserID,
		"", // email - should be fetched from user
		"", // username - should be fetched from user
		"user", // role - should be fetched from user
		now,
		accessTokenExpiry,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate new access token: %w", err)
	}

	return &auth.TokenPair{
		AccessToken:  accessToken,
		RefreshToken: newRefreshTokenString,
		ExpiresAt:    accessTokenExpiry,
	}, nil
}

// RevokeRefreshToken revokes a refresh token
func (s *TokenService) RevokeRefreshToken(ctx context.Context, refreshToken string) error {
	return s.refreshTokenRepo.RevokeByToken(ctx, refreshToken)
}

// RevokeAllUserTokens revokes all tokens for a user
func (s *TokenService) RevokeAllUserTokens(ctx context.Context, userID uuid.UUID) error {
	return s.refreshTokenRepo.RevokeAllByUserID(ctx, userID)
}

// generateAccessToken creates a JWT access token
func (s *TokenService) generateAccessToken(userID uuid.UUID, email, username, role string, issuedAt, expiresAt time.Time) (string, error) {
	claims := jwt.MapClaims{
		"user_id":  userID.String(),
		"email":    email,
		"username": username,
		"role":     role,
		"iat":      issuedAt.Unix(),
		"exp":      expiresAt.Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.secretKey)
}

// generateRandomToken generates a cryptographically secure random token
func (s *TokenService) generateRandomToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}
