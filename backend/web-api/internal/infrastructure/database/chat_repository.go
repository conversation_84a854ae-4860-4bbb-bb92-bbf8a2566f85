package database

import (
	"context"

	"gorm.io/gorm"

	"github.com/go-auggie-trial/web-api/internal/domain/chat"
	"github.com/google/uuid"
)

// ChatRepository implements chat.Repository interface
type ChatRepository struct {
	db *gorm.DB
}

// NewChatRepository creates a new ChatRepository
func NewChatRepository(db *gorm.DB) chat.Repository {
	return &ChatRepository{db: db}
}

// Create creates a new message
func (r *ChatRepository) Create(ctx context.Context, message *chat.Message) error {
	if err := r.db.WithContext(ctx).Create(message).Error; err != nil {
		return err
	}
	return nil
}

// GetByID retrieves a message by ID
func (r *ChatRepository) GetByID(ctx context.Context, id uuid.UUID) (*chat.Message, error) {
	var message chat.Message
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&message).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, chat.ErrMessageNotFound
		}
		return nil, err
	}
	return &message, nil
}

// GetByUserID retrieves messages for a specific user with pagination
func (r *ChatRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*chat.Message, error) {
	var messages []*chat.Message
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&messages).Error; err != nil {
		return nil, err
	}
	return messages, nil
}

// List retrieves messages with pagination
func (r *ChatRepository) List(ctx context.Context, limit, offset int) ([]*chat.Message, error) {
	var messages []*chat.Message
	if err := r.db.WithContext(ctx).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&messages).Error; err != nil {
		return nil, err
	}
	return messages, nil
}

// Update updates an existing message
func (r *ChatRepository) Update(ctx context.Context, message *chat.Message) error {
	if err := r.db.WithContext(ctx).Save(message).Error; err != nil {
		return err
	}
	return nil
}

// Delete deletes a message by ID
func (r *ChatRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&chat.Message{}, id).Error; err != nil {
		return err
	}
	return nil
}

// DeleteByUserID deletes all messages for a user
func (r *ChatRepository) DeleteByUserID(ctx context.Context, userID uuid.UUID) error {
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Delete(&chat.Message{}).Error; err != nil {
		return err
	}
	return nil
}

// Count returns the total number of messages
func (r *ChatRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&chat.Message{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// CountByUserID returns the total number of messages for a user
func (r *ChatRepository) CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).
		Model(&chat.Message{}).
		Where("user_id = ?", userID).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetRecent retrieves the most recent messages with pagination
func (r *ChatRepository) GetRecent(ctx context.Context, limit, offset int) ([]*chat.Message, error) {
	var messages []*chat.Message
	if err := r.db.WithContext(ctx).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&messages).Error; err != nil {
		return nil, err
	}
	return messages, nil
}
