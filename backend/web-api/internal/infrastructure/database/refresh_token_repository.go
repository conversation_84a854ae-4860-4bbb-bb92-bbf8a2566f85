package database

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/go-auggie-trial/web-api/internal/domain/auth"
	"github.com/google/uuid"
)

// RefreshTokenRepository implements auth.RefreshTokenRepository interface
type RefreshTokenRepository struct {
	db *gorm.DB
}

// NewRefreshTokenRepository creates a new RefreshTokenRepository
func NewRefreshTokenRepository(db *gorm.DB) auth.RefreshTokenRepository {
	return &RefreshTokenRepository{db: db}
}

// Create stores a new refresh token
func (r *RefreshTokenRepository) Create(ctx context.Context, token *auth.RefreshToken) error {
	if err := r.db.WithContext(ctx).Create(token).Error; err != nil {
		return err
	}
	return nil
}

// GetByToken retrieves a refresh token by token string
func (r *RefreshTokenRepository) GetByToken(ctx context.Context, token string) (*auth.RefreshToken, error) {
	var rt auth.RefreshToken
	if err := r.db.WithContext(ctx).Where("token = ?", token).First(&rt).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, auth.ErrTokenNotFound
		}
		return nil, err
	}
	return &rt, nil
}

// GetByUserID retrieves all refresh tokens for a user
func (r *RefreshTokenRepository) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*auth.RefreshToken, error) {
	var tokens []*auth.RefreshToken
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&tokens).Error; err != nil {
		return nil, err
	}
	return tokens, nil
}

// Update updates a refresh token
func (r *RefreshTokenRepository) Update(ctx context.Context, token *auth.RefreshToken) error {
	if err := r.db.WithContext(ctx).Save(token).Error; err != nil {
		return err
	}
	return nil
}

// Delete deletes a refresh token
func (r *RefreshTokenRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&auth.RefreshToken{}, id).Error; err != nil {
		return err
	}
	return nil
}

// DeleteByUserID deletes all refresh tokens for a user
func (r *RefreshTokenRepository) DeleteByUserID(ctx context.Context, userID uuid.UUID) error {
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Delete(&auth.RefreshToken{}).Error; err != nil {
		return err
	}
	return nil
}

// DeleteExpired deletes all expired refresh tokens
func (r *RefreshTokenRepository) DeleteExpired(ctx context.Context) error {
	if err := r.db.WithContext(ctx).
		Where("expires_at < ?", time.Now()).
		Delete(&auth.RefreshToken{}).Error; err != nil {
		return err
	}
	return nil
}

// RevokeByToken revokes a refresh token by token string
func (r *RefreshTokenRepository) RevokeByToken(ctx context.Context, token string) error {
	if err := r.db.WithContext(ctx).
		Model(&auth.RefreshToken{}).
		Where("token = ?", token).
		Update("is_revoked", true).Error; err != nil {
		return err
	}
	return nil
}

// RevokeAllByUserID revokes all refresh tokens for a user
func (r *RefreshTokenRepository) RevokeAllByUserID(ctx context.Context, userID uuid.UUID) error {
	if err := r.db.WithContext(ctx).
		Model(&auth.RefreshToken{}).
		Where("user_id = ?", userID).
		Update("is_revoked", true).Error; err != nil {
		return err
	}
	return nil
}
