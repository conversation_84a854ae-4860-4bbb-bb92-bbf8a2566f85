package usecase

import (
	"context"

	"github.com/go-auggie-trial/web-api/internal/domain/auth"
	"github.com/go-auggie-trial/web-api/internal/domain/user"
	"github.com/google/uuid"
)

// AuthUseCase handles authentication business logic
type AuthUseCase struct {
	userRepo         user.Repository
	refreshTokenRepo auth.RefreshTokenRepository
	tokenService     auth.TokenService
	passwordService  auth.PasswordService
}

// NewAuthUseCase creates a new AuthUseCase
func NewAuthUseCase(
	userRepo user.Repository,
	refreshTokenRepo auth.RefreshTokenRepository,
	tokenService auth.TokenService,
	passwordService auth.PasswordService,
) *AuthUseCase {
	return &AuthUseCase{
		userRepo:         userRepo,
		refreshTokenRepo: refreshTokenRepo,
		tokenService:     tokenService,
		passwordService:  passwordService,
	}
}

// Login authenticates a user and returns tokens
func (uc *AuthUseCase) Login(ctx context.Context, req *auth.LoginRequest) (*auth.TokenPair, error) {
	// Get user by email
	u, err := uc.userRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		return nil, auth.ErrInvalidCredentials
	}

	// Check if user is active
	if !u.IsActive {
		return nil, user.ErrUserInactive
	}

	// Verify password
	if err := uc.passwordService.ComparePassword(u.Password, req.Password); err != nil {
		return nil, auth.ErrInvalidCredentials
	}

	// Generate token pair
	tokenPair, err := uc.tokenService.GenerateTokenPair(ctx, u.ID, u.Email, u.Username, u.Role.String())
	if err != nil {
		return nil, err
	}

	return tokenPair, nil
}

// Register creates a new user account
func (uc *AuthUseCase) Register(ctx context.Context, req *auth.RegisterRequest) (*user.User, error) {
	// Check if user already exists by email
	exists, err := uc.userRepo.ExistsByEmail(ctx, req.Email)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, user.ErrUserAlreadyExists
	}

	// Check if username is taken
	exists, err = uc.userRepo.ExistsByUsername(ctx, req.Username)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, user.ErrUserAlreadyExists
	}

	// Hash password
	hashedPassword, err := uc.passwordService.HashPassword(req.Password)
	if err != nil {
		return nil, err
	}

	// Create user
	newUser := &user.User{
		ID:        uuid.New(),
		Email:     req.Email,
		Username:  req.Username,
		Password:  hashedPassword,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Role:      user.RoleUser,
		IsActive:  true,
	}

	// Validate user
	if err := newUser.Validate(); err != nil {
		return nil, err
	}

	// Save user
	if err := uc.userRepo.Create(ctx, newUser); err != nil {
		return nil, err
	}

	return newUser, nil
}

// RefreshToken generates new tokens using refresh token
func (uc *AuthUseCase) RefreshToken(ctx context.Context, refreshToken string) (*auth.TokenPair, error) {
	return uc.tokenService.RefreshAccessToken(ctx, refreshToken)
}

// Logout revokes the refresh token
func (uc *AuthUseCase) Logout(ctx context.Context, refreshToken string) error {
	return uc.tokenService.RevokeRefreshToken(ctx, refreshToken)
}

// LogoutAll revokes all tokens for a user
func (uc *AuthUseCase) LogoutAll(ctx context.Context, userID uuid.UUID) error {
	return uc.tokenService.RevokeAllUserTokens(ctx, userID)
}

// ValidateToken validates an access token and returns claims
func (uc *AuthUseCase) ValidateToken(ctx context.Context, token string) (*auth.Claims, error) {
	return uc.tokenService.ValidateAccessToken(ctx, token)
}
