package usecase

import (
	"context"

	"github.com/go-auggie-trial/web-api/internal/domain/chat"
	"github.com/google/uuid"
)

// ChatUseCase handles chat business logic
type ChatUseCase struct {
	chatRepo chat.Repository
}

// NewChatUseCase creates a new ChatUseCase
func NewChatUseCase(chatRepo chat.Repository) *ChatUseCase {
	return &ChatUseCase{
		chatRepo: chatRepo,
	}
}

// SendMessage creates and stores a new chat message
func (uc *ChatUseCase) SendMessage(ctx context.Context, userID uuid.UUID, req *chat.ChatRequest) (*chat.ChatResponse, error) {
	// Create message entity
	message := &chat.Message{
		ID:      uuid.New(),
		UserID:  userID,
		Content: req.Content,
		Type:    req.Type,
	}

	// Validate message
	if err := message.Validate(); err != nil {
		return nil, err
	}

	// Save message
	if err := uc.chatRepo.Create(ctx, message); err != nil {
		return nil, err
	}

	return message.ToResponse(), nil
}

// GetMessage retrieves a message by ID
func (uc *ChatUseCase) GetMessage(ctx context.Context, messageID uuid.UUID, userID uuid.UUID) (*chat.ChatResponse, error) {
	message, err := uc.chatRepo.GetByID(ctx, messageID)
	if err != nil {
		return nil, err
	}

	// Check if user has access to this message (only their own messages)
	if message.UserID != userID {
		return nil, chat.ErrUnauthorizedAccess
	}

	return message.ToResponse(), nil
}

// GetUserMessages retrieves messages for a specific user with pagination
func (uc *ChatUseCase) GetUserMessages(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*chat.ChatResponse, error) {
	messages, err := uc.chatRepo.GetByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, err
	}

	responses := make([]*chat.ChatResponse, len(messages))
	for i, message := range messages {
		responses[i] = message.ToResponse()
	}

	return responses, nil
}

// GetRecentMessages retrieves the most recent messages with pagination (admin only)
func (uc *ChatUseCase) GetRecentMessages(ctx context.Context, limit, offset int) ([]*chat.ChatResponse, error) {
	messages, err := uc.chatRepo.GetRecent(ctx, limit, offset)
	if err != nil {
		return nil, err
	}

	responses := make([]*chat.ChatResponse, len(messages))
	for i, message := range messages {
		responses[i] = message.ToResponse()
	}

	return responses, nil
}

// UpdateMessage updates an existing message (only content can be updated)
func (uc *ChatUseCase) UpdateMessage(ctx context.Context, messageID uuid.UUID, userID uuid.UUID, content string) (*chat.ChatResponse, error) {
	// Get existing message
	message, err := uc.chatRepo.GetByID(ctx, messageID)
	if err != nil {
		return nil, err
	}

	// Check if user owns this message
	if message.UserID != userID {
		return nil, chat.ErrUnauthorizedAccess
	}

	// Update content
	message.Content = content

	// Validate updated message
	if err := message.Validate(); err != nil {
		return nil, err
	}

	// Save updated message
	if err := uc.chatRepo.Update(ctx, message); err != nil {
		return nil, err
	}

	return message.ToResponse(), nil
}

// DeleteMessage deletes a message
func (uc *ChatUseCase) DeleteMessage(ctx context.Context, messageID uuid.UUID, userID uuid.UUID) error {
	// Get existing message
	message, err := uc.chatRepo.GetByID(ctx, messageID)
	if err != nil {
		return err
	}

	// Check if user owns this message
	if message.UserID != userID {
		return chat.ErrUnauthorizedAccess
	}

	return uc.chatRepo.Delete(ctx, messageID)
}

// DeleteUserMessages deletes all messages for a user
func (uc *ChatUseCase) DeleteUserMessages(ctx context.Context, userID uuid.UUID) error {
	return uc.chatRepo.DeleteByUserID(ctx, userID)
}

// CountUserMessages returns the total number of messages for a user
func (uc *ChatUseCase) CountUserMessages(ctx context.Context, userID uuid.UUID) (int64, error) {
	return uc.chatRepo.CountByUserID(ctx, userID)
}

// CountAllMessages returns the total number of messages (admin only)
func (uc *ChatUseCase) CountAllMessages(ctx context.Context) (int64, error) {
	return uc.chatRepo.Count(ctx)
}
