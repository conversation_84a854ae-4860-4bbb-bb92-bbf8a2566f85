package usecase

import (
	"context"

	"github.com/go-auggie-trial/web-api/internal/domain/user"
	"github.com/google/uuid"
)

// UserUseCase handles user management business logic
type UserUseCase struct {
	userRepo user.Repository
}

// NewUserUseCase creates a new UserUseCase
func NewUserUseCase(userRepo user.Repository) *UserUseCase {
	return &UserUseCase{
		userRepo: userRepo,
	}
}

// GetUserByID retrieves a user by ID
func (uc *UserUseCase) GetUserByID(ctx context.Context, id uuid.UUID) (*user.User, error) {
	return uc.userRepo.GetByID(ctx, id)
}

// GetUserByEmail retrieves a user by email
func (uc *UserUseCase) GetUserByEmail(ctx context.Context, email string) (*user.User, error) {
	return uc.userRepo.GetByEmail(ctx, email)
}

// GetUserByUsername retrieves a user by username
func (uc *UserUseCase) GetUserByUsername(ctx context.Context, username string) (*user.User, error) {
	return uc.userRepo.GetByUsername(ctx, username)
}

// UpdateUser updates user information
func (uc *UserUseCase) UpdateUser(ctx context.Context, u *user.User) error {
	// Validate user data
	if err := u.Validate(); err != nil {
		return err
	}

	// Check if user exists
	existingUser, err := uc.userRepo.GetByID(ctx, u.ID)
	if err != nil {
		return err
	}

	// Check if email is being changed and if it's already taken
	if existingUser.Email != u.Email {
		exists, err := uc.userRepo.ExistsByEmail(ctx, u.Email)
		if err != nil {
			return err
		}
		if exists {
			return user.ErrUserAlreadyExists
		}
	}

	// Check if username is being changed and if it's already taken
	if existingUser.Username != u.Username {
		exists, err := uc.userRepo.ExistsByUsername(ctx, u.Username)
		if err != nil {
			return err
		}
		if exists {
			return user.ErrUserAlreadyExists
		}
	}

	return uc.userRepo.Update(ctx, u)
}

// DeactivateUser deactivates a user account
func (uc *UserUseCase) DeactivateUser(ctx context.Context, id uuid.UUID) error {
	u, err := uc.userRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	u.IsActive = false
	return uc.userRepo.Update(ctx, u)
}

// ActivateUser activates a user account
func (uc *UserUseCase) ActivateUser(ctx context.Context, id uuid.UUID) error {
	u, err := uc.userRepo.GetByID(ctx, id)
	if err != nil {
		return err
	}

	u.IsActive = true
	return uc.userRepo.Update(ctx, u)
}

// DeleteUser deletes a user account
func (uc *UserUseCase) DeleteUser(ctx context.Context, id uuid.UUID) error {
	return uc.userRepo.Delete(ctx, id)
}

// ListUsers retrieves users with pagination
func (uc *UserUseCase) ListUsers(ctx context.Context, limit, offset int) ([]*user.User, error) {
	return uc.userRepo.List(ctx, limit, offset)
}

// CountUsers returns the total number of users
func (uc *UserUseCase) CountUsers(ctx context.Context) (int64, error) {
	return uc.userRepo.Count(ctx)
}

// UserProfile represents user profile information (without sensitive data)
type UserProfile struct {
	ID        uuid.UUID `json:"id"`
	Email     string    `json:"email"`
	Username  string    `json:"username"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Role      string    `json:"role"`
	IsActive  bool      `json:"is_active"`
}

// GetUserProfile returns user profile without sensitive information
func (uc *UserUseCase) GetUserProfile(ctx context.Context, id uuid.UUID) (*UserProfile, error) {
	u, err := uc.userRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return &UserProfile{
		ID:        u.ID,
		Email:     u.Email,
		Username:  u.Username,
		FirstName: u.FirstName,
		LastName:  u.LastName,
		Role:      u.Role.String(),
		IsActive:  u.IsActive,
	}, nil
}
