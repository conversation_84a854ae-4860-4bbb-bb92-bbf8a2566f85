package dto

import (
	"time"

	"github.com/google/uuid"
)

// ChatMessageRequest represents a chat message request
type ChatMessageRequest struct {
	Content string `json:"content" validate:"required,min=1,max=1000"`
	Type    string `json:"type" validate:"required,oneof=text image file"`
}

// ChatMessageResponse represents a chat message response
type ChatMessageResponse struct {
	ID        uuid.UUID `json:"id"`
	UserID    uuid.UUID `json:"user_id"`
	Content   string    `json:"content"`
	Type      string    `json:"type"`
	CreatedAt time.Time `json:"created_at"`
}

// UpdateMessageRequest represents an update message request
type UpdateMessageRequest struct {
	Content string `json:"content" validate:"required,min=1,max=1000"`
}

// ChatMessagesResponse represents a paginated list of messages
type ChatMessagesResponse struct {
	Messages   []ChatMessageResponse `json:"messages"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"page_size"`
	TotalPages int                   `json:"total_pages"`
}

// MessageStatsResponse represents message statistics
type MessageStatsResponse struct {
	TotalMessages     int64 `json:"total_messages"`
	UserMessages      int64 `json:"user_messages"`
	MessagesToday     int64 `json:"messages_today"`
	MessagesThisWeek  int64 `json:"messages_this_week"`
	MessagesThisMonth int64 `json:"messages_this_month"`
}
