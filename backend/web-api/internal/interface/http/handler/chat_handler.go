package handler

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/google/uuid"

	"github.com/go-auggie-trial/web-api/internal/application/usecase"
	"github.com/go-auggie-trial/web-api/internal/domain/chat"
	"github.com/go-auggie-trial/web-api/internal/interface/dto"
	"github.com/go-auggie-trial/web-api/pkg/validator"
)

// <PERSON><PERSON><PERSON><PERSON><PERSON> handles chat HTTP requests
type ChatHandler struct {
	chatUseCase *usecase.ChatUseCase
	validator   *validator.Validator
}

// NewChatHandler creates a new ChatHandler
func NewChatHandler(chatUseCase *usecase.ChatUseCase, validator *validator.Validator) *ChatHandler {
	return &ChatHandler{
		chatUseCase: chatUseCase,
		validator:   validator,
	}
}

// SendMessage handles sending a chat message
func (h *ChatHandler) SendMessage(c echo.Context) error {
	// Get user ID from context (set by auth middleware)
	userID, ok := c.Get("user_id").(uuid.UUID)
	if !ok {
		return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"UNAUTHORIZED",
			"User ID not found in context",
			"",
		))
	}

	var req dto.ChatMessageRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_REQUEST",
			"Invalid request format",
			err.Error(),
		))
	}

	if err := h.validator.Validate(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"VALIDATION_ERROR",
			"Validation failed",
			err.Error(),
		))
	}

	// Convert to domain request
	chatReq := &chat.ChatRequest{
		Content: req.Content,
		Type:    chat.MessageType(req.Type),
	}

	response, err := h.chatUseCase.SendMessage(c.Request().Context(), userID, chatReq)
	if err != nil {
		switch err {
		case chat.ErrInvalidContent:
			return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
				"INVALID_CONTENT",
				"Message content is invalid",
				"",
			))
		case chat.ErrInvalidMessageType:
			return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
				"INVALID_MESSAGE_TYPE",
				"Message type is invalid",
				"",
			))
		default:
			return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
				"INTERNAL_ERROR",
				"Failed to send message",
				err.Error(),
			))
		}
	}

	// Convert to DTO
	messageResponse := dto.ChatMessageResponse{
		ID:        response.ID,
		UserID:    response.UserID,
		Content:   response.Content,
		Type:      string(response.Type),
		CreatedAt: response.CreatedAt,
	}

	return c.JSON(http.StatusCreated, dto.SuccessResponse("Message sent successfully", messageResponse))
}

// GetUserMessages retrieves messages for the authenticated user
func (h *ChatHandler) GetUserMessages(c echo.Context) error {
	// Get user ID from context
	userID, ok := c.Get("user_id").(uuid.UUID)
	if !ok {
		return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"UNAUTHORIZED",
			"User ID not found in context",
			"",
		))
	}

	// Parse pagination parameters
	var pagination dto.PaginationRequest
	if err := c.Bind(&pagination); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_REQUEST",
			"Invalid pagination parameters",
			err.Error(),
		))
	}

	// Set defaults
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.PageSize <= 0 {
		pagination.PageSize = 10
	}

	messages, err := h.chatUseCase.GetUserMessages(
		c.Request().Context(),
		userID,
		pagination.GetLimit(),
		pagination.GetOffset(),
	)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
			"INTERNAL_ERROR",
			"Failed to retrieve messages",
			err.Error(),
		))
	}

	// Get total count
	total, err := h.chatUseCase.CountUserMessages(c.Request().Context(), userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
			"INTERNAL_ERROR",
			"Failed to count messages",
			err.Error(),
		))
	}

	// Convert to DTOs
	messageResponses := make([]dto.ChatMessageResponse, len(messages))
	for i, msg := range messages {
		messageResponses[i] = dto.ChatMessageResponse{
			ID:        msg.ID,
			UserID:    msg.UserID,
			Content:   msg.Content,
			Type:      string(msg.Type),
			CreatedAt: msg.CreatedAt,
		}
	}

	response := dto.ChatMessagesResponse{
		Messages:   messageResponses,
		Total:      total,
		Page:       pagination.Page,
		PageSize:   pagination.PageSize,
		TotalPages: dto.CalculateTotalPages(total, pagination.PageSize),
	}

	return c.JSON(http.StatusOK, dto.SuccessResponse("Messages retrieved successfully", response))
}

// GetMessage retrieves a specific message
func (h *ChatHandler) GetMessage(c echo.Context) error {
	// Get user ID from context
	userID, ok := c.Get("user_id").(uuid.UUID)
	if !ok {
		return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"UNAUTHORIZED",
			"User ID not found in context",
			"",
		))
	}

	// Parse message ID
	messageIDStr := c.Param("id")
	messageID, err := uuid.Parse(messageIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_MESSAGE_ID",
			"Invalid message ID format",
			err.Error(),
		))
	}

	message, err := h.chatUseCase.GetMessage(c.Request().Context(), messageID, userID)
	if err != nil {
		switch err {
		case chat.ErrMessageNotFound:
			return c.JSON(http.StatusNotFound, dto.ErrorResponse(
				"MESSAGE_NOT_FOUND",
				"Message not found",
				"",
			))
		case chat.ErrUnauthorizedAccess:
			return c.JSON(http.StatusForbidden, dto.ErrorResponse(
				"FORBIDDEN",
				"Access denied to this message",
				"",
			))
		default:
			return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
				"INTERNAL_ERROR",
				"Failed to retrieve message",
				err.Error(),
			))
		}
	}

	messageResponse := dto.ChatMessageResponse{
		ID:        message.ID,
		UserID:    message.UserID,
		Content:   message.Content,
		Type:      string(message.Type),
		CreatedAt: message.CreatedAt,
	}

	return c.JSON(http.StatusOK, dto.SuccessResponse("Message retrieved successfully", messageResponse))
}

// UpdateMessage updates a message
func (h *ChatHandler) UpdateMessage(c echo.Context) error {
	// Get user ID from context
	userID, ok := c.Get("user_id").(uuid.UUID)
	if !ok {
		return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"UNAUTHORIZED",
			"User ID not found in context",
			"",
		))
	}

	// Parse message ID
	messageIDStr := c.Param("id")
	messageID, err := uuid.Parse(messageIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_MESSAGE_ID",
			"Invalid message ID format",
			err.Error(),
		))
	}

	var req dto.UpdateMessageRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_REQUEST",
			"Invalid request format",
			err.Error(),
		))
	}

	if err := h.validator.Validate(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"VALIDATION_ERROR",
			"Validation failed",
			err.Error(),
		))
	}

	message, err := h.chatUseCase.UpdateMessage(c.Request().Context(), messageID, userID, req.Content)
	if err != nil {
		switch err {
		case chat.ErrMessageNotFound:
			return c.JSON(http.StatusNotFound, dto.ErrorResponse(
				"MESSAGE_NOT_FOUND",
				"Message not found",
				"",
			))
		case chat.ErrUnauthorizedAccess:
			return c.JSON(http.StatusForbidden, dto.ErrorResponse(
				"FORBIDDEN",
				"Access denied to this message",
				"",
			))
		default:
			return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
				"INTERNAL_ERROR",
				"Failed to update message",
				err.Error(),
			))
		}
	}

	messageResponse := dto.ChatMessageResponse{
		ID:        message.ID,
		UserID:    message.UserID,
		Content:   message.Content,
		Type:      string(message.Type),
		CreatedAt: message.CreatedAt,
	}

	return c.JSON(http.StatusOK, dto.SuccessResponse("Message updated successfully", messageResponse))
}

// DeleteMessage deletes a message
func (h *ChatHandler) DeleteMessage(c echo.Context) error {
	// Get user ID from context
	userID, ok := c.Get("user_id").(uuid.UUID)
	if !ok {
		return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
			"UNAUTHORIZED",
			"User ID not found in context",
			"",
		))
	}

	// Parse message ID
	messageIDStr := c.Param("id")
	messageID, err := uuid.Parse(messageIDStr)
	if err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_MESSAGE_ID",
			"Invalid message ID format",
			err.Error(),
		))
	}

	err = h.chatUseCase.DeleteMessage(c.Request().Context(), messageID, userID)
	if err != nil {
		switch err {
		case chat.ErrMessageNotFound:
			return c.JSON(http.StatusNotFound, dto.ErrorResponse(
				"MESSAGE_NOT_FOUND",
				"Message not found",
				"",
			))
		case chat.ErrUnauthorizedAccess:
			return c.JSON(http.StatusForbidden, dto.ErrorResponse(
				"FORBIDDEN",
				"Access denied to this message",
				"",
			))
		default:
			return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
				"INTERNAL_ERROR",
				"Failed to delete message",
				err.Error(),
			))
		}
	}

	return c.JSON(http.StatusOK, dto.SuccessResponse("Message deleted successfully", nil))
}
