package handler

import (
	"net/http"

	"github.com/labstack/echo/v4"

	"github.com/go-auggie-trial/web-api/internal/application/usecase"
	"github.com/go-auggie-trial/web-api/internal/domain/auth"
	"github.com/go-auggie-trial/web-api/internal/domain/user"
	"github.com/go-auggie-trial/web-api/internal/interface/dto"
	"github.com/go-auggie-trial/web-api/pkg/validator"
)

// AuthHandler handles authentication HTTP requests
type AuthHandler struct {
	authUseCase *usecase.AuthUseCase
	validator   *validator.Validator
}

// NewAuthHandler creates a new AuthHandler
func NewAuthHandler(authUseCase *usecase.AuthUseCase, validator *validator.Validator) *AuthHandler {
	return &AuthHandler{
		authUseCase: authUseCase,
		validator:   validator,
	}
}

// <PERSON>gin handles user login
func (h *AuthHandler) Login(c echo.Context) error {
	var req dto.LoginRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_REQUEST",
			"Invalid request format",
			err.Error(),
		))
	}

	if err := h.validator.Validate(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"VALIDATION_ERROR",
			"Validation failed",
			err.Error(),
		))
	}

	// Convert to domain request
	loginReq := &auth.LoginRequest{
		Email:    req.Email,
		Password: req.Password,
	}

	tokenPair, err := h.authUseCase.Login(c.Request().Context(), loginReq)
	if err != nil {
		switch err {
		case auth.ErrInvalidCredentials:
			return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
				"INVALID_CREDENTIALS",
				"Invalid email or password",
				"",
			))
		case user.ErrUserInactive:
			return c.JSON(http.StatusForbidden, dto.ErrorResponse(
				"USER_INACTIVE",
				"User account is inactive",
				"",
			))
		default:
			return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
				"INTERNAL_ERROR",
				"Login failed",
				err.Error(),
			))
		}
	}

	// Get user info (you'd typically fetch this from the user service)
	// For now, we'll create a minimal response
	response := dto.LoginResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt,
		User: dto.UserInfo{
			Email: req.Email,
		},
	}

	return c.JSON(http.StatusOK, dto.SuccessResponse("Login successful", response))
}

// Register handles user registration
func (h *AuthHandler) Register(c echo.Context) error {
	var req dto.RegisterRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_REQUEST",
			"Invalid request format",
			err.Error(),
		))
	}

	if err := h.validator.Validate(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"VALIDATION_ERROR",
			"Validation failed",
			err.Error(),
		))
	}

	// Convert to domain request
	registerReq := &auth.RegisterRequest{
		Email:     req.Email,
		Username:  req.Username,
		Password:  req.Password,
		FirstName: req.FirstName,
		LastName:  req.LastName,
	}

	newUser, err := h.authUseCase.Register(c.Request().Context(), registerReq)
	if err != nil {
		switch err {
		case user.ErrUserAlreadyExists:
			return c.JSON(http.StatusConflict, dto.ErrorResponse(
				"USER_EXISTS",
				"User with this email or username already exists",
				"",
			))
		default:
			return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
				"INTERNAL_ERROR",
				"Registration failed",
				err.Error(),
			))
		}
	}

	userInfo := dto.UserInfo{
		ID:        newUser.ID,
		Email:     newUser.Email,
		Username:  newUser.Username,
		FirstName: newUser.FirstName,
		LastName:  newUser.LastName,
		Role:      newUser.Role.String(),
		IsActive:  newUser.IsActive,
	}

	response := dto.RegisterResponse{
		Message: "Registration successful",
		User:    userInfo,
	}

	return c.JSON(http.StatusCreated, dto.SuccessResponse("User registered successfully", response))
}

// RefreshToken handles token refresh
func (h *AuthHandler) RefreshToken(c echo.Context) error {
	var req dto.RefreshTokenRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_REQUEST",
			"Invalid request format",
			err.Error(),
		))
	}

	if err := h.validator.Validate(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"VALIDATION_ERROR",
			"Validation failed",
			err.Error(),
		))
	}

	tokenPair, err := h.authUseCase.RefreshToken(c.Request().Context(), req.RefreshToken)
	if err != nil {
		switch err {
		case auth.ErrTokenNotFound, auth.ErrTokenExpired, auth.ErrTokenRevoked:
			return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
				"INVALID_REFRESH_TOKEN",
				"Invalid or expired refresh token",
				"",
			))
		default:
			return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
				"INTERNAL_ERROR",
				"Token refresh failed",
				err.Error(),
			))
		}
	}

	response := dto.RefreshTokenResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    tokenPair.ExpiresAt,
	}

	return c.JSON(http.StatusOK, dto.SuccessResponse("Token refreshed successfully", response))
}

// Logout handles user logout
func (h *AuthHandler) Logout(c echo.Context) error {
	var req dto.LogoutRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"INVALID_REQUEST",
			"Invalid request format",
			err.Error(),
		))
	}

	if err := h.validator.Validate(&req); err != nil {
		return c.JSON(http.StatusBadRequest, dto.ErrorResponse(
			"VALIDATION_ERROR",
			"Validation failed",
			err.Error(),
		))
	}

	if err := h.authUseCase.Logout(c.Request().Context(), req.RefreshToken); err != nil {
		return c.JSON(http.StatusInternalServerError, dto.ErrorResponse(
			"INTERNAL_ERROR",
			"Logout failed",
			err.Error(),
		))
	}

	return c.JSON(http.StatusOK, dto.SuccessResponse("Logout successful", nil))
}
