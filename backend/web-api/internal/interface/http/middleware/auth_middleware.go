package middleware

import (
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"

	"github.com/go-auggie-trial/web-api/internal/application/usecase"
	"github.com/go-auggie-trial/web-api/internal/domain/auth"
	"github.com/go-auggie-trial/web-api/internal/interface/dto"
)

// AuthMiddleware handles JWT authentication
type AuthMiddleware struct {
	authUseCase *usecase.AuthUseCase
}

// NewAuthMiddleware creates a new AuthMiddleware
func NewAuthMiddleware(authUseCase *usecase.AuthUseCase) *AuthMiddleware {
	return &AuthMiddleware{
		authUseCase: authUseCase,
	}
}

// JWTAuth middleware validates JWT tokens
func (m *AuthMiddleware) JWTAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get token from Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
					"UNAUTHORIZED",
					"Authorization header is required",
					"",
				))
			}

			// Check if header starts with "Bearer "
			if !strings.HasPrefix(authHeader, "Bearer ") {
				return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
					"INVALID_TOKEN_FORMAT",
					"Authorization header must start with 'Bearer '",
					"",
				))
			}

			// Extract token
			token := strings.TrimPrefix(authHeader, "Bearer ")
			if token == "" {
				return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
					"INVALID_TOKEN",
					"Token is required",
					"",
				))
			}

			// Validate token
			claims, err := m.authUseCase.ValidateToken(c.Request().Context(), token)
			if err != nil {
				switch err {
				case auth.ErrTokenExpired:
					return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
						"TOKEN_EXPIRED",
						"Token has expired",
						"",
					))
				case auth.ErrTokenInvalid:
					return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
						"INVALID_TOKEN",
						"Token is invalid",
						"",
					))
				default:
					return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
						"UNAUTHORIZED",
						"Authentication failed",
						err.Error(),
					))
				}
			}

			// Set user claims in context
			c.Set("user_id", claims.UserID)
			c.Set("user_email", claims.Email)
			c.Set("user_username", claims.Username)
			c.Set("user_role", claims.Role)

			return next(c)
		}
	}
}

// AdminOnly middleware ensures only admin users can access the endpoint
func (m *AuthMiddleware) AdminOnly() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userRole := c.Get("user_role")
			if userRole == nil {
				return c.JSON(http.StatusUnauthorized, dto.ErrorResponse(
					"UNAUTHORIZED",
					"User role not found in context",
					"",
				))
			}

			role, ok := userRole.(string)
			if !ok || role != "admin" {
				return c.JSON(http.StatusForbidden, dto.ErrorResponse(
					"FORBIDDEN",
					"Admin access required",
					"",
				))
			}

			return next(c)
		}
	}
}

// OptionalAuth middleware validates JWT tokens but doesn't require them
func (m *AuthMiddleware) OptionalAuth() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get token from Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return next(c)
			}

			// Check if header starts with "Bearer "
			if !strings.HasPrefix(authHeader, "Bearer ") {
				return next(c)
			}

			// Extract token
			token := strings.TrimPrefix(authHeader, "Bearer ")
			if token == "" {
				return next(c)
			}

			// Validate token
			claims, err := m.authUseCase.ValidateToken(c.Request().Context(), token)
			if err == nil {
				// Set user claims in context if token is valid
				c.Set("user_id", claims.UserID)
				c.Set("user_email", claims.Email)
				c.Set("user_username", claims.Username)
				c.Set("user_role", claims.Role)
			}

			return next(c)
		}
	}
}
