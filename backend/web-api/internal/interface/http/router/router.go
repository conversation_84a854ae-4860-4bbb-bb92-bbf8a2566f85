package router

import (
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	"github.com/go-auggie-trial/web-api/internal/interface/dto"
	"github.com/go-auggie-trial/web-api/internal/interface/http/handler"
	authMiddleware "github.com/go-auggie-trial/web-api/internal/interface/http/middleware"
)

// Router holds all the handlers and middleware
type Router struct {
	authHandler    *handler.AuthHandler
	chatHandler    *handler.ChatHandler
	authMiddleware *authMiddleware.AuthMiddleware
}

// NewRouter creates a new router with all handlers
func NewRouter(
	authHandler *handler.AuthHandler,
	chatHandler *handler.ChatHandler,
	authMiddleware *authMiddleware.AuthMiddleware,
) *Router {
	return &Router{
		authHandler:    authHandler,
		chatHandler:    chatHandler,
		authMiddleware: authMiddleware,
	}
}

// SetupRoutes configures all routes for the Echo server
func (r *Router) SetupRoutes(e *echo.Echo) {
	// Middleware
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())
	e.Use(middleware.RequestID())
	e.Use(middleware.TimeoutWithConfig(middleware.TimeoutConfig{
		Timeout: 30 * time.Second,
	}))

	// Health check endpoint
	e.GET("/health", r.healthCheck)

	// API v1 routes
	v1 := e.Group("/api/v1")

	// Public routes (no authentication required)
	public := v1.Group("")
	r.setupPublicRoutes(public)

	// Protected routes (authentication required)
	protected := v1.Group("")
	protected.Use(r.authMiddleware.JWTAuth())
	r.setupProtectedRoutes(protected)

	// Admin routes (admin role required)
	admin := v1.Group("/admin")
	admin.Use(r.authMiddleware.JWTAuth())
	admin.Use(r.authMiddleware.AdminOnly())
	r.setupAdminRoutes(admin)
}

// setupPublicRoutes configures public routes
func (r *Router) setupPublicRoutes(g *echo.Group) {
	// Authentication routes
	auth := g.Group("/auth")
	auth.POST("/login", r.authHandler.Login)
	auth.POST("/register", r.authHandler.Register)
	auth.POST("/refresh", r.authHandler.RefreshToken)
	auth.POST("/logout", r.authHandler.Logout)
}

// setupProtectedRoutes configures protected routes
func (r *Router) setupProtectedRoutes(g *echo.Group) {
	// Chat routes
	chat := g.Group("/chat")
	chat.POST("/messages", r.chatHandler.SendMessage)
	chat.GET("/messages", r.chatHandler.GetUserMessages)
	chat.GET("/messages/:id", r.chatHandler.GetMessage)
	chat.PUT("/messages/:id", r.chatHandler.UpdateMessage)
	chat.DELETE("/messages/:id", r.chatHandler.DeleteMessage)

	// User profile routes (you can add these later)
	// user := g.Group("/user")
	// user.GET("/profile", r.userHandler.GetProfile)
	// user.PUT("/profile", r.userHandler.UpdateProfile)
}

// setupAdminRoutes configures admin-only routes
func (r *Router) setupAdminRoutes(g *echo.Group) {
	// Admin chat management
	chat := g.Group("/chat")
	chat.GET("/messages", r.chatHandler.GetUserMessages) // Admin can see all messages
	
	// Admin user management (you can add these later)
	// users := g.Group("/users")
	// users.GET("", r.userHandler.ListUsers)
	// users.GET("/:id", r.userHandler.GetUser)
	// users.PUT("/:id", r.userHandler.UpdateUser)
	// users.DELETE("/:id", r.userHandler.DeleteUser)
}

// healthCheck handles health check requests
func (r *Router) healthCheck(c echo.Context) error {
	response := dto.HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now().UTC().Format(time.RFC3339),
		Version:   "1.0.0",
		Database:  "connected", // You could add actual database health check here
	}

	return c.JSON(http.StatusOK, dto.SuccessResponse("Service is healthy", response))
}
