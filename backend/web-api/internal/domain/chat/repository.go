package chat

import (
	"context"

	"github.com/google/uuid"
)

// Repository defines the interface for chat message data access
type Repository interface {
	// Create creates a new message
	Create(ctx context.Context, message *Message) error

	// GetByID retrieves a message by ID
	GetByID(ctx context.Context, id uuid.UUID) (*Message, error)

	// GetByUserID retrieves messages for a specific user with pagination
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*Message, error)

	// List retrieves messages with pagination
	List(ctx context.Context, limit, offset int) ([]*Message, error)

	// Update updates an existing message
	Update(ctx context.Context, message *Message) error

	// Delete deletes a message by ID
	Delete(ctx context.Context, id uuid.UUID) error

	// DeleteByUserID deletes all messages for a user
	DeleteByUserID(ctx context.Context, userID uuid.UUID) error

	// Count returns the total number of messages
	Count(ctx context.Context) (int64, error)

	// CountByUserID returns the total number of messages for a user
	CountByUserID(ctx context.Context, userID uuid.UUID) (int64, error)

	// GetRecent retrieves the most recent messages with pagination
	GetRecent(ctx context.Context, limit, offset int) ([]*Message, error)
}
