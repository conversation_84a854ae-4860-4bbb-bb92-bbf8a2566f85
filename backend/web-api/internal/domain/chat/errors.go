package chat

import "errors"

// Domain errors for chat
var (
	ErrMessageNotFound     = errors.New("message not found")
	ErrInvalidContent      = errors.New("invalid message content")
	ErrInvalidMessageType  = errors.New("invalid message type")
	ErrInvalidUserID       = errors.New("invalid user ID")
	ErrMessageTooLong      = errors.New("message content too long")
	ErrUnauthorizedAccess  = errors.New("unauthorized access to message")
)
