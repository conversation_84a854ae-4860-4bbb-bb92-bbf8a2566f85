package chat

import (
	"time"

	"github.com/google/uuid"
)

// Message represents a chat message
type Message struct {
	ID        uuid.UUID   `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID    uuid.UUID   `json:"user_id" gorm:"type:uuid;not null;index"`
	Content   string      `json:"content" gorm:"type:text;not null"`
	Type      MessageType `json:"type" gorm:"type:varchar(20);not null;default:'text'"`
	CreatedAt time.Time   `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time   `json:"updated_at" gorm:"autoUpdateTime"`
}

// MessageType represents the type of message
type MessageType string

const (
	MessageTypeText  MessageType = "text"
	MessageTypeImage MessageType = "image"
	MessageTypeFile  MessageType = "file"
)

// IsValid checks if the message type is valid
func (mt MessageType) IsValid() bool {
	switch mt {
	case MessageTypeText, MessageTypeImage, MessageTypeFile:
		return true
	default:
		return false
	}
}

// String returns the string representation of the message type
func (mt MessageType) String() string {
	return string(mt)
}

// ChatRequest represents a chat message request
type ChatRequest struct {
	Content string      `json:"content" validate:"required,min=1,max=1000"`
	Type    MessageType `json:"type" validate:"required"`
}

// ChatResponse represents a chat message response
type ChatResponse struct {
	ID        uuid.UUID   `json:"id"`
	UserID    uuid.UUID   `json:"user_id"`
	Content   string      `json:"content"`
	Type      MessageType `json:"type"`
	CreatedAt time.Time   `json:"created_at"`
}

// Validate performs basic validation on message entity
func (m *Message) Validate() error {
	if m.Content == "" {
		return ErrInvalidContent
	}
	if !m.Type.IsValid() {
		return ErrInvalidMessageType
	}
	if m.UserID == uuid.Nil {
		return ErrInvalidUserID
	}
	return nil
}

// ToResponse converts Message to ChatResponse
func (m *Message) ToResponse() *ChatResponse {
	return &ChatResponse{
		ID:        m.ID,
		UserID:    m.UserID,
		Content:   m.Content,
		Type:      m.Type,
		CreatedAt: m.CreatedAt,
	}
}
