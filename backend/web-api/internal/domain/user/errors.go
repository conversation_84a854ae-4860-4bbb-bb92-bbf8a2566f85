package user

import "errors"

// Domain errors for user
var (
	ErrUserNotFound      = errors.New("user not found")
	ErrUserAlreadyExists = errors.New("user already exists")
	ErrInvalidEmail      = errors.New("invalid email")
	ErrInvalidUsername   = errors.New("invalid username")
	ErrInvalidFirstName  = errors.New("invalid first name")
	ErrInvalidLastName   = errors.New("invalid last name")
	ErrInvalidRole       = errors.New("invalid role")
	ErrInvalidPassword   = errors.New("invalid password")
	ErrUserInactive      = errors.New("user is inactive")
)
