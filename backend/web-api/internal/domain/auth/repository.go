package auth

import (
	"context"

	"github.com/google/uuid"
)

// RefreshTokenRepository defines the interface for refresh token data access
type RefreshTokenRepository interface {
	// Create stores a new refresh token
	Create(ctx context.Context, token *RefreshToken) error

	// GetByT<PERSON> retrieves a refresh token by token string
	GetByToken(ctx context.Context, token string) (*RefreshToken, error)

	// GetByUserID retrieves all refresh tokens for a user
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*RefreshToken, error)

	// Update updates a refresh token
	Update(ctx context.Context, token *RefreshToken) error

	// Delete deletes a refresh token
	Delete(ctx context.Context, id uuid.UUID) error

	// DeleteByUserID deletes all refresh tokens for a user
	DeleteByUserID(ctx context.Context, userID uuid.UUID) error

	// DeleteExpired deletes all expired refresh tokens
	DeleteExpired(ctx context.Context) error

	// RevokeByToken revokes a refresh token by token string
	RevokeByToken(ctx context.Context, token string) error

	// RevokeAllByUserID revokes all refresh tokens for a user
	RevokeAllByUserID(ctx context.Context, userID uuid.UUID) error
}

// TokenService defines the interface for token operations
type TokenService interface {
	// GenerateTokenPair generates access and refresh tokens for a user
	GenerateTokenPair(ctx context.Context, userID uuid.UUID, email, username, role string) (*TokenPair, error)

	// ValidateAccessToken validates an access token and returns claims
	ValidateAccessToken(ctx context.Context, token string) (*Claims, error)

	// RefreshAccessToken generates a new access token using refresh token
	RefreshAccessToken(ctx context.Context, refreshToken string) (*TokenPair, error)

	// RevokeRefreshToken revokes a refresh token
	RevokeRefreshToken(ctx context.Context, refreshToken string) error

	// RevokeAllUserTokens revokes all tokens for a user
	RevokeAllUserTokens(ctx context.Context, userID uuid.UUID) error
}

// PasswordService defines the interface for password operations
type PasswordService interface {
	// HashPassword hashes a plain text password
	HashPassword(password string) (string, error)

	// ComparePassword compares a plain text password with a hashed password
	ComparePassword(hashedPassword, password string) error
}
