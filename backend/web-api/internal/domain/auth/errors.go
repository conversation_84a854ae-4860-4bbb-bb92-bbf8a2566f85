package auth

import "errors"

// Domain errors for authentication
var (
	ErrInvalidCredentials = errors.New("invalid credentials")
	ErrTokenExpired       = errors.New("token expired")
	ErrTokenInvalid       = errors.New("token invalid")
	ErrTokenRevoked       = errors.New("token revoked")
	ErrTokenNotFound      = errors.New("token not found")
	ErrUnauthorized       = errors.New("unauthorized")
	ErrForbidden          = errors.New("forbidden")
	ErrInvalidTokenFormat = errors.New("invalid token format")
	ErrTokenGenerationFailed = errors.New("token generation failed")
)
