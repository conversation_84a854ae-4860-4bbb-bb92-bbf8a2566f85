# CircleCI Setup for Augment PR Reviews

This guide will help you set up automated AI-powered code reviews using Augment on CircleCI.

## 🚀 Quick Start

### Prerequisites

1. **Auggie CLI installed locally**:
   ```bash
   npm install -g @augmentcode/auggie
   auggie --login  # Login to your Augment account
   ```

2. **GitHub repository** with CircleCI enabled

3. **GitHub Personal Access Token** with `repo` scope

### Step 1: Get Your Augment Token

Run this command to get your authentication token:

```bash
auggie --print-augment-token
```

Copy the JSON value after `TOKEN=` - you'll need this for CircleCI.

### Step 2: Set Up CircleCI Context

1. Go to [CircleCI Contexts](https://app.circleci.com/settings/organization/github/YOUR_ORG/contexts)
2. Create a new context called `augment-context`
3. Add these environment variables:

   **AUGMENT_SESSION_AUTH**
   ```json
   {"accessToken":"your-token-here","tenantURL":"https://your-tenant.api.augmentcode.com","scopes":["read","write"]}
   ```

   **GITHUB_TOKEN**
   ```
   your_github_personal_access_token
   ```

### Step 3: Enable CircleCI for Your Repository

1. Go to [CircleCI Projects](https://app.circleci.com/projects/)
2. Find your repository and click "Set Up Project"
3. Choose "Use Existing Config" (we already have `.circleci/config.yml`)

### Step 4: Test the Setup

1. Create a new branch and make some code changes
2. Open a pull request
3. CircleCI should automatically run and post an AI-generated review

## 📁 Configuration Files

### Main Configuration (`.circleci/config.yml`)

The main configuration includes:
- ✅ Comprehensive PR analysis
- ✅ Structured review format
- ✅ Error handling and fallbacks
- ✅ Detailed logging

### Simple Configuration (`.circleci/config-simple.yml`)

A minimal setup for basic use cases:
- ✅ Quick setup
- ✅ Essential features only
- ✅ Easier to understand and modify

To use the simple version:
```bash
cp .circleci/config-simple.yml .circleci/config.yml
```

## 🔧 Customization

### Custom Review Guidelines

Edit the instruction in `.circleci/config.yml` to customize the review criteria:

```yaml
# Create comprehensive instruction for Auggie
cat > /tmp/review_instruction.md << 'EOF'
You are an expert Go code reviewer. Focus on:
1. Go idioms and best practices
2. Error handling patterns
3. Concurrency safety
4. Performance considerations
5. Security vulnerabilities
EOF
```

### Trigger Conditions

Modify the workflow filters to change when reviews run:

```yaml
workflows:
  pr-review-workflow:
    jobs:
      - augment-pr-review:
          filters:
            branches:
              ignore: 
                - master
                - main
                - develop  # Add more branches to ignore
```

### Review Format

Customize the review output format by modifying the instruction template in the config file.

## 🛠️ Troubleshooting

### Common Issues

1. **"Not a pull request, skipping review"**
   - This is normal for pushes to main/master branches
   - Reviews only run on pull requests

2. **Authentication failed**
   - Check that `AUGMENT_SESSION_AUTH` is correctly set in CircleCI context
   - Verify the token format matches the example above

3. **GitHub API errors**
   - Ensure `GITHUB_TOKEN` has `repo` scope
   - Check if the token is expired

4. **Auggie command not found**
   - The config installs Auggie automatically
   - Check CircleCI logs for installation errors

### Debug Mode

Add this step to your config for debugging:

```yaml
- run:
    name: Debug Info
    command: |
      echo "PR Number: $PR_NUMBER"
      echo "Repo: $REPO_NAME"
      echo "Auggie version: $(auggie --version)"
      gh auth status
```

## 🔒 Security Best Practices

1. **Never commit tokens to code**
   - Always use CircleCI contexts for sensitive data
   - Use environment variables, not hardcoded values

2. **Minimal permissions**
   - GitHub token should only have `repo` scope
   - Don't grant unnecessary permissions

3. **Token rotation**
   - Regularly rotate your GitHub tokens
   - Update Augment tokens if they expire

## 📊 Monitoring

### Check Review Quality

Monitor your reviews to ensure they're helpful:
- Are the suggestions actionable?
- Do they catch real issues?
- Are they too verbose or too brief?

### Performance Metrics

Track these metrics:
- Review generation time
- Success rate
- Developer feedback

## 🆘 Support

If you need help:

1. **Check CircleCI logs** for detailed error messages
2. **Run the setup script**: `./scripts/setup-circleci-augment.sh`
3. **Verify your Augment account** is active and has API access
4. **Test Auggie locally** to ensure it's working

## 🎯 Next Steps

Once your setup is working:

1. **Customize review guidelines** for your team's needs
2. **Set up notifications** for failed reviews
3. **Create team-specific contexts** for different projects
4. **Integrate with other CI/CD tools** in your pipeline

## 📝 Example Review Output

Here's what a typical AI-generated review looks like:

```markdown
## 🤖 Augment AI Code Review

**PR:** #123 | **Repository:** owner/repo | **Reviewed by:** Auggie AI

---

### 🔍 Overall Assessment
This PR introduces a new user authentication system with good structure but has some security and performance concerns.

### ✅ Strengths
- Clean separation of concerns
- Good error handling in most places
- Comprehensive test coverage

### ⚠️ Issues Found
- `auth.go:45` - Password stored in plain text
- `user.go:123` - SQL injection vulnerability
- `handler.go:67` - Missing input validation

### 💡 Suggestions
1. Use bcrypt for password hashing
2. Implement prepared statements for database queries
3. Add input sanitization middleware

### 🏁 Conclusion
Request changes - Security issues must be addressed before merging.

---
*This review was generated automatically by Augment AI. Please use your judgment and verify suggestions.*
```

Happy coding! 🚀
