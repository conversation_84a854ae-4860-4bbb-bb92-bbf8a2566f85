import { AxiosResponse } from 'axios';
import { api } from './authService';
import { 
  Conversation, 
  Message, 
  SendMessageRequest, 
  SendMessageResponse 
} from '../types';

export const chatService = {
  async getMessages(page: number = 1, limit: number = 50): Promise<Message[]> {
    try {
      const response: AxiosResponse<{ messages: Message[] }> = await api.get('/chat/messages', {
        params: { page, limit }
      });
      return response.data.messages || [];
    } catch (error) {
      throw error;
    }
  },

  async getMessage(messageId: string): Promise<Message> {
    try {
      const response: AxiosResponse<{ message: Message }> = await api.get(`/chat/messages/${messageId}`);
      return response.data.message;
    } catch (error) {
      throw error;
    }
  },

  async sendMessage(content: string, type: string = 'text'): Promise<Message> {
    try {
      const response: AxiosResponse<{ message: Message }> = await api.post('/chat/messages', {
        content,
        type
      });
      return response.data.message;
    } catch (error) {
      throw error;
    }
  },

  async updateMessage(messageId: string, content: string): Promise<Message> {
    try {
      const response: AxiosResponse<{ message: Message }> = await api.put(`/chat/messages/${messageId}`, {
        content
      });
      return response.data.message;
    } catch (error) {
      throw error;
    }
  },

  async deleteMessage(messageId: string): Promise<void> {
    try {
      await api.delete(`/chat/messages/${messageId}`);
    } catch (error) {
      throw error;
    }
  },

  // Helper method to group messages into conversations (client-side logic)
  async getConversations(): Promise<Conversation[]> {
    try {
      const messages = await this.getMessages();

      // Group messages by date or create a single conversation for now
      // This is a simplified implementation - you might want to enhance this
      const conversation: Conversation = {
        id: 'default-conversation',
        title: 'Chat Session',
        messages: messages,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: 'current-user' // This should come from auth context
      };

      return [conversation];
    } catch (error) {
      throw error;
    }
  },

  async getConversation(conversationId: string): Promise<Conversation> {
    try {
      const conversations = await this.getConversations();
      const conversation = conversations.find(c => c.id === conversationId);

      if (!conversation) {
        throw new Error('Conversation not found');
      }

      return conversation;
    } catch (error) {
      throw error;
    }
  },
};
