import axios, { AxiosResponse } from 'axios';
import { LoginRequest, RegisterRequest, AuthResponse, ApiError } from '../types';
import { API_BASE_URL } from '../utils/constants';
import { tokenUtils } from '../utils/helpers';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = tokenUtils.get();
    if (token && tokenUtils.isValid(token)) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid, clear auth data
      tokenUtils.remove();
      window.location.href = '/login';
    }
    
    const apiError: ApiError = {
      message: error.response?.data?.message || error.message || 'An error occurred',
      status: error.response?.status || 500,
      code: error.response?.data?.code,
    };
    
    return Promise.reject(apiError);
  }
);

export const authService = {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response: AxiosResponse<any> = await api.post('/auth/login', credentials);
      const data = response.data;

      // Transform backend response to frontend format
      return {
        user: {
          ...data.user,
          name: `${data.user.first_name} ${data.user.last_name}`.trim(),
          createdAt: data.user.created_at,
          updatedAt: data.user.updated_at,
        },
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        token: data.access_token, // Legacy compatibility
      };
    } catch (error) {
      throw error;
    }
  },

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      // Transform frontend data to match backend expected format
      const backendData = {
        email: userData.email,
        password: userData.password,
        username: userData.email.split('@')[0], // Use email prefix as username
        first_name: userData.name.split(' ')[0] || userData.name,
        last_name: userData.name.split(' ').slice(1).join(' ') || '',
      };

      const response: AxiosResponse<any> = await api.post('/auth/register', backendData);
      const data = response.data;

      // Transform backend response to frontend format
      return {
        user: {
          ...data.user,
          name: `${data.user.first_name} ${data.user.last_name}`.trim(),
          createdAt: data.user.created_at,
          updatedAt: data.user.updated_at,
        },
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        token: data.access_token, // Legacy compatibility
      };
    } catch (error) {
      throw error;
    }
  },

  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // Even if logout fails on server, we should clear local data
      console.error('Logout error:', error);
    }
  },

  async refreshToken(): Promise<AuthResponse> {
    try {
      const response: AxiosResponse<AuthResponse> = await api.post('/auth/refresh');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  async getCurrentUser(): Promise<AuthResponse['user']> {
    try {
      const response: AxiosResponse<{ user: AuthResponse['user'] }> = await api.get('/auth/me');
      return response.data.user;
    } catch (error) {
      throw error;
    }
  },
};

// Export the configured axios instance for use in other services
export { api };
