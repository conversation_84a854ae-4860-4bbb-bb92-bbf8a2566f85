import React, { createContext, useContext, useReducer, useCallback, ReactNode } from 'react';
import { Conversation, Message, ChatState } from '../types';
import { chatService } from '../services/chatService';
import { generateId } from '../utils/helpers';

interface ChatContextType extends ChatState {
  loadConversations: () => Promise<void>;
  selectConversation: (conversationId: string) => Promise<void>;
  sendMessage: (content: string) => Promise<void>;
  createNewConversation: () => Promise<void>;
  deleteConversation: (conversationId: string) => Promise<void>;
  clearError: () => void;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

type ChatAction =
  | { type: 'CHAT_LOADING'; payload: boolean }
  | { type: 'CHAT_ERROR'; payload: string }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_CONVERSATIONS'; payload: Conversation[] }
  | { type: 'SET_CURRENT_CONVERSATION'; payload: Conversation | null }
  | { type: 'ADD_CONVERSATION'; payload: Conversation }
  | { type: 'UPDATE_CONVERSATION'; payload: Conversation }
  | { type: 'REMOVE_CONVERSATION'; payload: string }
  | { type: 'ADD_MESSAGE'; payload: { conversationId: string; message: Message } }
  | { type: 'ADD_TEMPORARY_MESSAGE'; payload: { conversationId: string; message: Message } }
  | { type: 'UPDATE_MESSAGE'; payload: { conversationId: string; messageId: string; message: Message } };

const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'CHAT_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'CHAT_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    case 'SET_CONVERSATIONS':
      return { ...state, conversations: action.payload, isLoading: false };
    
    case 'SET_CURRENT_CONVERSATION':
      return { ...state, currentConversation: action.payload, isLoading: false };
    
    case 'ADD_CONVERSATION':
      return {
        ...state,
        conversations: [action.payload, ...state.conversations],
        currentConversation: action.payload,
        isLoading: false,
      };
    
    case 'UPDATE_CONVERSATION':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.id ? action.payload : conv
        ),
        currentConversation: state.currentConversation?.id === action.payload.id
          ? action.payload
          : state.currentConversation,
      };
    
    case 'REMOVE_CONVERSATION':
      const filteredConversations = state.conversations.filter(conv => conv.id !== action.payload);
      return {
        ...state,
        conversations: filteredConversations,
        currentConversation: state.currentConversation?.id === action.payload
          ? (filteredConversations[0] || null)
          : state.currentConversation,
      };
    
    case 'ADD_MESSAGE':
    case 'ADD_TEMPORARY_MESSAGE':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.conversationId
            ? { ...conv, messages: [...conv.messages, action.payload.message] }
            : conv
        ),
        currentConversation: state.currentConversation?.id === action.payload.conversationId
          ? {
              ...state.currentConversation,
              messages: [...state.currentConversation.messages, action.payload.message],
            }
          : state.currentConversation,
      };
    
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.conversationId
            ? {
                ...conv,
                messages: conv.messages.map(msg =>
                  msg.id === action.payload.messageId ? action.payload.message : msg
                ),
              }
            : conv
        ),
        currentConversation: state.currentConversation?.id === action.payload.conversationId
          ? {
              ...state.currentConversation,
              messages: state.currentConversation.messages.map(msg =>
                msg.id === action.payload.messageId ? action.payload.message : msg
              ),
            }
          : state.currentConversation,
      };
    
    default:
      return state;
  }
};

const initialState: ChatState = {
  conversations: [],
  currentConversation: null,
  isLoading: false,
  error: null,
};

interface ChatProviderProps {
  children: ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(chatReducer, initialState);

  const loadConversations = useCallback(async () => {
    dispatch({ type: 'CHAT_LOADING', payload: true });
    try {
      const conversations = await chatService.getConversations();
      dispatch({ type: 'SET_CONVERSATIONS', payload: conversations });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load conversations';
      dispatch({ type: 'CHAT_ERROR', payload: errorMessage });
    }
  }, []);

  const selectConversation = useCallback(async (conversationId: string) => {
    dispatch({ type: 'CHAT_LOADING', payload: true });
    try {
      const conversation = await chatService.getConversation(conversationId);
      dispatch({ type: 'SET_CURRENT_CONVERSATION', payload: conversation });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load conversation';
      dispatch({ type: 'CHAT_ERROR', payload: errorMessage });
    }
  }, []);

  const sendMessage = useCallback(async (content: string) => {
    if (!state.currentConversation) return;

    // Add temporary user message
    const tempUserMessage: Message = {
      id: generateId(),
      content,
      type: 'text',
      role: 'user',
      user_id: 'current-user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      timestamp: new Date().toISOString(),
      conversationId: state.currentConversation.id,
    };

    dispatch({
      type: 'ADD_TEMPORARY_MESSAGE',
      payload: { conversationId: state.currentConversation.id, message: tempUserMessage },
    });

    try {
      const sentMessage = await chatService.sendMessage(content);

      // Transform backend message to frontend format
      const transformedMessage: Message = {
        ...sentMessage,
        role: 'user',
        timestamp: sentMessage.created_at,
        conversationId: state.currentConversation.id,
      };

      // Replace temporary message with actual message
      dispatch({
        type: 'UPDATE_MESSAGE',
        payload: {
          conversationId: state.currentConversation.id,
          messageId: tempUserMessage.id,
          message: transformedMessage,
        },
      });

      // TODO: Here you would typically call your AI service to get a response
      // For now, we'll simulate an AI response
      setTimeout(() => {
        const aiResponse: Message = {
          id: generateId(),
          content: "I'm a simulated AI response. Connect me to your AI backend!",
          type: 'text',
          role: 'assistant',
          user_id: 'ai-assistant',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          timestamp: new Date().toISOString(),
          conversationId: state.currentConversation!.id,
        };

        dispatch({
          type: 'ADD_MESSAGE',
          payload: { conversationId: state.currentConversation!.id, message: aiResponse },
        });
      }, 1000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      dispatch({ type: 'CHAT_ERROR', payload: errorMessage });
    }
  }, [state.currentConversation]);

  const createNewConversation = useCallback(async () => {
    dispatch({ type: 'CHAT_LOADING', payload: true });
    try {
      const conversation = await chatService.createConversation();
      dispatch({ type: 'ADD_CONVERSATION', payload: conversation });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create conversation';
      dispatch({ type: 'CHAT_ERROR', payload: errorMessage });
    }
  }, []);

  const deleteConversation = useCallback(async (conversationId: string) => {
    try {
      await chatService.deleteConversation(conversationId);
      dispatch({ type: 'REMOVE_CONVERSATION', payload: conversationId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete conversation';
      dispatch({ type: 'CHAT_ERROR', payload: errorMessage });
    }
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  const value: ChatContextType = {
    ...state,
    loadConversations,
    selectConversation,
    sendMessage,
    createNewConversation,
    deleteConversation,
    clearError,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
