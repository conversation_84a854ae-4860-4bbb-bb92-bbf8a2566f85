import React, { useState, useEffect } from 'react';
import { Header } from './Header';
import { ConversationList } from '../chat/ConversationList';
import { useChat } from '../../contexts/ChatContext';
import { UI_CONFIG } from '../../utils/constants';

interface LayoutProps {
  children: React.ReactNode;
  showSidebar?: boolean;
}

export const Layout: React.FC<LayoutProps> = ({ children, showSidebar = true }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  
  const {
    conversations,
    currentConversation,
    loadConversations,
    selectConversation,
    createNewConversation,
    deleteConversation,
  } = useChat();

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < UI_CONFIG.MOBILE_BREAKPOINT);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Load conversations on mount
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  // Close sidebar on mobile when conversation is selected
  useEffect(() => {
    if (isMobile && currentConversation) {
      setSidebarOpen(false);
    }
  }, [currentConversation, isMobile]);

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSelectConversation = (conversationId: string) => {
    selectConversation(conversationId);
  };

  const handleNewConversation = () => {
    createNewConversation();
  };

  const handleDeleteConversation = (conversationId: string) => {
    deleteConversation(conversationId);
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <Header 
        onToggleSidebar={handleToggleSidebar}
        showSidebarToggle={showSidebar}
      />

      {/* Main content area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        {showSidebar && (
          <>
            {/* Desktop sidebar */}
            <div className={`hidden lg:flex lg:flex-shrink-0 ${sidebarOpen ? 'lg:w-80' : 'lg:w-80'}`}>
              <div className="w-full">
                <ConversationList
                  conversations={conversations}
                  currentConversationId={currentConversation?.id}
                  onSelectConversation={handleSelectConversation}
                  onNewConversation={handleNewConversation}
                />
              </div>
            </div>

            {/* Mobile sidebar overlay */}
            {sidebarOpen && (
              <div className="fixed inset-0 z-40 lg:hidden">
                {/* Overlay */}
                <div 
                  className="fixed inset-0 bg-gray-600 bg-opacity-75"
                  onClick={() => setSidebarOpen(false)}
                />
                
                {/* Sidebar */}
                <div className="relative flex flex-col w-full max-w-xs bg-white h-full">
                  <ConversationList
                    conversations={conversations}
                    currentConversationId={currentConversation?.id}
                    onSelectConversation={handleSelectConversation}
                    onNewConversation={handleNewConversation}
                  />
                </div>
              </div>
            )}
          </>
        )}

        {/* Main content */}
        <div className="flex-1 flex flex-col min-w-0">
          {children}
        </div>
      </div>
    </div>
  );
};
