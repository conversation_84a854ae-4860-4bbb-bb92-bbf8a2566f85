import React, { useState } from 'react';
import { MessageSquare, Plus, Trash2, MoreVertical } from 'lucide-react';
import { ConversationListProps } from '../../types';
import { formatDate, truncateText } from '../../utils/helpers';

export const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  currentConversationId,
  onSelectConversation,
  onNewConversation,
}) => {
  const [showDeleteMenu, setShowDeleteMenu] = useState<string | null>(null);

  const handleDeleteClick = (e: React.MouseEvent, conversationId: string) => {
    e.stopPropagation();
    setShowDeleteMenu(showDeleteMenu === conversationId ? null : conversationId);
  };

  const handleDelete = (e: React.MouseEvent, conversationId: string) => {
    e.stopPropagation();
    // This would be handled by parent component
    console.log('Delete conversation:', conversationId);
    setShowDeleteMenu(null);
  };

  return (
    <div className="flex flex-col h-full bg-gray-50 border-r border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <button
          onClick={onNewConversation}
          className="w-full flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          New Conversation
        </button>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        {conversations.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <MessageSquare className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No conversations yet</p>
            <p className="text-xs text-gray-400 mt-1">Start a new conversation to get started</p>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {conversations.map((conversation) => {
              const isActive = conversation.id === currentConversationId;
              const lastMessage = conversation.messages[conversation.messages.length - 1];
              
              return (
                <div
                  key={conversation.id}
                  onClick={() => onSelectConversation(conversation.id)}
                  className={`relative group p-3 rounded-lg cursor-pointer transition-colors ${
                    isActive
                      ? 'bg-primary-100 border border-primary-200'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className={`text-sm font-medium truncate ${
                        isActive ? 'text-primary-900' : 'text-gray-900'
                      }`}>
                        {conversation.title}
                      </h3>
                      
                      {lastMessage && (
                        <p className={`text-xs mt-1 truncate ${
                          isActive ? 'text-primary-700' : 'text-gray-600'
                        }`}>
                          {lastMessage.role === 'user' ? 'You: ' : 'AI: '}
                          {truncateText(lastMessage.content, 50)}
                        </p>
                      )}
                      
                      <p className={`text-xs mt-1 ${
                        isActive ? 'text-primary-600' : 'text-gray-500'
                      }`}>
                        {formatDate(conversation.updatedAt)}
                      </p>
                    </div>

                    {/* More options button */}
                    <div className="relative">
                      <button
                        onClick={(e) => handleDeleteClick(e, conversation.id)}
                        className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-gray-200 transition-opacity"
                      >
                        <MoreVertical className="w-4 h-4 text-gray-500" />
                      </button>

                      {/* Delete menu */}
                      {showDeleteMenu === conversation.id && (
                        <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                          <button
                            onClick={(e) => handleDelete(e, conversation.id)}
                            className="flex items-center gap-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg"
                          >
                            <Trash2 className="w-4 h-4" />
                            Delete
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};
