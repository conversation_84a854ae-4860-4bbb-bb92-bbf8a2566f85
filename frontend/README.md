# ChatBot Frontend

A modern React-based chatbot application built with TypeScript, Vite, and TailwindCSS.

## Features

- 🔐 **Authentication System**: Login/logout functionality with JWT tokens
- 💬 **Chat Interface**: Real-time chat with AI assistant
- 📱 **Responsive Design**: Mobile-first design that works on all devices
- 🎨 **Modern UI**: Clean interface built with TailwindCSS
- 🔒 **Protected Routes**: Secure routing with authentication guards
- 👤 **User Management**: Profile management and settings
- 💾 **Conversation History**: Save and manage chat conversations
- ⚡ **Fast Development**: Built with Vite for lightning-fast HMR

## Tech Stack

- **React 18** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **TailwindCSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **React Hook Form** - Form management
- **Axios** - HTTP client
- **Lucide React** - Icon library

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd frontend
```

2. Install dependencies
```bash
npm install
```

3. Create environment file
```bash
cp .env.example .env
```

4. Update environment variables in `.env`:
```env
VITE_API_BASE_URL=http://localhost:8080/api
```

5. Start the development server
```bash
npm run dev
```

The application will be available at `http://localhost:5173/`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── chat/           # Chat-related components
│   ├── layout/         # Layout components
│   └── ui/             # Basic UI components
├── contexts/           # React contexts for state management
├── hooks/              # Custom React hooks
├── pages/              # Page components
├── services/           # API services
├── types/              # TypeScript type definitions
├── utils/              # Utility functions and constants
└── main.tsx           # Application entry point
```

## Key Components

### Authentication
- **LoginPage**: User login form
- **RegisterPage**: User registration form
- **ProtectedRoute**: Route guard for authenticated users
- **AuthContext**: Authentication state management

### Chat Interface
- **ChatContainer**: Main chat interface
- **ChatMessage**: Individual message component
- **ChatInput**: Message input with auto-resize
- **ConversationList**: Sidebar with conversation history

### Layout
- **Layout**: Main application layout
- **Header**: Top navigation with user menu

## API Integration

The frontend communicates with the backend API through:

- **authService**: Authentication endpoints
- **chatService**: Chat and conversation endpoints

All API calls include automatic token management and error handling.

## Styling

The application uses TailwindCSS with:

- Custom color palette (primary blue theme)
- Responsive design utilities
- Custom component classes
- Dark mode support (ready for implementation)

## Development

### Adding New Features

1. Create components in appropriate directories
2. Add TypeScript types in `src/types/`
3. Update routing in `App.tsx` if needed
4. Add API services in `src/services/`

### State Management

The application uses React Context for state management:

- **AuthContext**: User authentication state
- **ChatContext**: Chat and conversation state

## Building for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

## Environment Variables

- `VITE_API_BASE_URL`: Backend API base URL
- `VITE_APP_NAME`: Application name
- `VITE_APP_VERSION`: Application version

## Contributing

1. Follow the existing code style
2. Add TypeScript types for new features
3. Test components thoroughly
4. Update documentation as needed

## License

This project is licensed under the MIT License.
